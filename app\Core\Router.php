<?php
/**
 * Simple Router class for handling routes
 */

class Router {
    private static $routes = [];
    private static $middlewares = [];
    
    /**
     * Add a GET route
     */
    public static function get($path, $handler, $middleware = null) {
        self::addRoute('GET', $path, $handler, $middleware);
    }
    
    /**
     * Add a POST route
     */
    public static function post($path, $handler, $middleware = null) {
        self::addRoute('POST', $path, $handler, $middleware);
    }
    
    /**
     * Add any method route
     */
    public static function any($path, $handler, $middleware = null) {
        self::addRoute('*', $path, $handler, $middleware);
    }
    
    /**
     * Add a route
     */
    private static function addRoute($method, $path, $handler, $middleware = null) {
        self::$routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'middleware' => $middleware
        ];
    }
    
    /**
     * Dispatch the request
     */
    public static function dispatch() {
        $requestMethod = $_SERVER['REQUEST_METHOD'];
        $requestPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Debug: Log the original request path
        if (isset($GLOBALS['config']['debug_mode']) && $GLOBALS['config']['debug_mode']) {
            error_log("Router Debug - Original path: " . $requestPath);
            error_log("Router Debug - Script name: " . $_SERVER['SCRIPT_NAME']);
        }
        
        // Remove base path if running in subdirectory
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/' && strpos($requestPath, $basePath) === 0) {
            $requestPath = substr($requestPath, strlen($basePath));
        }
        
        // Normalize path - handle empty path and remove double slashes
        $requestPath = '/' . trim($requestPath, '/');
        if ($requestPath === '/') {
            $requestPath = '/';
        }
        
        // Remove any double slashes that might have been introduced
        $requestPath = preg_replace('/\/+/', '/', $requestPath);
        
        // Debug: Log the normalized path
        if (isset($GLOBALS['config']['debug_mode']) && $GLOBALS['config']['debug_mode']) {
            error_log("Router Debug - Normalized path: " . $requestPath);
        }
        
        foreach (self::$routes as $route) {
            $routeMethod = $route['method'];
            $routePath = $route['path'];
            $handler = $route['handler'];
            $middleware = $route['middleware'];
            
            // Check if method matches
            if ($routeMethod !== '*' && $routeMethod !== $requestMethod) {
                continue;
            }
            
            // Check if path matches
            $matches = self::matchPath($routePath, $requestPath);
            if ($matches !== false) {
                // Execute middleware if present
                if ($middleware) {
                    if (is_string($middleware)) {
                        $middleware = [new $middleware(), 'handle'];
                    }
                    
                    $middlewareResult = call_user_func($middleware);
                    if ($middlewareResult === false) {
                        return;
                    }
                }
                
                // Execute handler
                if (is_string($handler)) {
                    // Handle Controller@method format
                    if (strpos($handler, '@') !== false) {
                        list($controllerName, $methodName) = explode('@', $handler);
                        $controllerClass = $controllerName . 'Controller';
                        
                        if (class_exists($controllerClass)) {
                            $controller = new $controllerClass();
                            
                            // Call before hook
                            if (method_exists($controller, 'before')) {
                                $controller->before();
                            }
                            
                            // Call the action
                            if (method_exists($controller, $methodName)) {
                                call_user_func_array([$controller, $methodName], $matches);
                            } else {
                                self::notFound();
                            }
                            
                            // Call after hook
                            if (method_exists($controller, 'after')) {
                                $controller->after();
                            }
                        } else {
                            self::notFound();
                        }
                    } else {
                        // Handle function name
                        if (function_exists($handler)) {
                            call_user_func_array($handler, $matches);
                        } else {
                            self::notFound();
                        }
                    }
                } else if (is_callable($handler)) {
                    // Handle closure
                    call_user_func_array($handler, $matches);
                }
                
                return;
            }
        }
        
        // No route found
        self::notFound();
    }
    
    /**
     * Simple path matching without regex
     */
    private static function matchPath($routePath, $requestPath) {
        // Handle exact matches first
        if ($routePath === $requestPath) {
            return [];
        }
        
        // Handle parameter routes like /users/{id}
        if (strpos($routePath, '{') !== false) {
            $routeParts = explode('/', trim($routePath, '/'));
            $requestParts = explode('/', trim($requestPath, '/'));
            
            // Must have same number of parts
            if (count($routeParts) !== count($requestParts)) {
                return false;
            }
            
            $parameters = [];
            for ($i = 0; $i < count($routeParts); $i++) {
                $routePart = $routeParts[$i];
                $requestPart = $requestParts[$i];
                
                // If it's a parameter (contains curly braces)
                if (strpos($routePart, '{') !== false && strpos($routePart, '}') !== false) {
                    $parameters[] = $requestPart;
                } else {
                    // Must match exactly
                    if ($routePart !== $requestPart) {
                        return false;
                    }
                }
            }
            
            return $parameters;
        }
        
        return false;
    }
    
    /**
     * Handle 404 Not Found
     */
    private static function notFound() {
        http_response_code(404);
        echo "404 - Page Not Found";
    }
    
    /**
     * Get all routes (for debugging)
     */
    public static function getRoutes() {
        return self::$routes;
    }
}
