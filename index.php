<?php
/**
 * Front controller
 *
 * This file handles all requests and routes them to the appropriate controller
 */
// Load configuration
require_once 'app/Config/config.php';

// Load helper functions
require_once 'app/Helpers/functions.php';

// Load core classes
require_once 'app/Core/DB.php';
require_once 'app/Core/Controller.php';
require_once 'app/Core/Router.php';

// Load controllers
require_once 'app/Controllers/UserController.php';
require_once 'app/Controllers/DashboardController.php';
require_once 'app/Controllers/TicketController.php';
require_once 'app/Controllers/NotificationController.php';

// Load routes
require_once 'app/routes.php';

// Dispatch the request
try {
    Router::dispatch();
} catch (Exception $e) {
    if ($config['debug_mode']) {
        echo "<h1>Error</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        echo "500 - Internal Server Error";
        logError($e->getMessage());
    }
}

