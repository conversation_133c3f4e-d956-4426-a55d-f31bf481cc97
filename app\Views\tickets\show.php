<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Ticket #<?= $ticket['ticket_number']; ?></h2>
                    <p class="mb-md-0"><?= sanitize($ticket['subject']); ?></p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <a href="<?= baseUrl('tickets'); ?>" class="btn btn-secondary mb-2">
                    <i class="mdi mdi-arrow-left"></i> Back to Tickets
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="ticket-header mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Category</h6>
                            <span class="badge badge-info"><?= sanitize($ticket['category_name']); ?></span>
                        </div>
                        <div class="col-md-6">
                            <h6>Priority</h6>
                            <span class="badge" style="background-color: <?= $ticket['priority_color']; ?>; color: white;">
                                <?= sanitize($ticket['priority_name']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="ticket-description mb-4">
                    <h6>Description</h6>
                    <div class="border rounded p-3">
                        <?= nl2br(sanitize($ticket['description'])); ?>
                    </div>
                </div>
                
                <!-- Ticket Attachments -->
                <?php if (!empty($attachments)): ?>
                <div class="ticket-attachments mb-4">
                    <h6 class="mb-3">Attachments (<?= count($attachments); ?>)</h6>
                    <div class="attachments-grid">
                        <?php foreach ($attachments as $attachment): ?>
                        <div class="attachment-item mb-3">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <div class="attachment-icon mr-3">
                                            <?php
                                            $icon = 'mdi-file';
                                            if (strpos($attachment['mime_type'], 'image/') === 0) {
                                                $icon = 'mdi-image';
                                            } elseif (strpos($attachment['mime_type'], 'video/') === 0) {
                                                $icon = 'mdi-video';
                                            } elseif (strpos($attachment['mime_type'], 'pdf') !== false) {
                                                $icon = 'mdi-file-pdf';
                                            } elseif (strpos($attachment['mime_type'], 'word') !== false || strpos($attachment['mime_type'], 'document') !== false) {
                                                $icon = 'mdi-file-word';
                                            } elseif (strpos($attachment['mime_type'], 'zip') !== false || strpos($attachment['mime_type'], 'rar') !== false) {
                                                $icon = 'mdi-zip-box';
                                            }
                                            ?>
                                            <i class="mdi <?= $icon; ?> text-primary" style="font-size: 2rem;"></i>
                                        </div>
                                        <div class="attachment-info flex-grow-1">
                                            <div class="font-weight-bold"><?= sanitize($attachment['original_filename']); ?></div>
                                            <small class="text-muted">
                                                <?= number_format($attachment['file_size'] / 1024 / 1024, 2); ?> MB • 
                                                Uploaded by <?= sanitize($attachment['uploaded_by_name']); ?> • 
                                                <?= date('M d, Y H:i', strtotime($attachment['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="attachment-actions">
                                            <a href="<?= baseUrl('tickets/attachment/' . $attachment['id'] . '/download'); ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="mdi mdi-download"></i> Download
                                            </a>
                                        </div>
                                    </div>
                                    
                                    <!-- Preview for images -->
                                    <?php if (strpos($attachment['mime_type'], 'image/') === 0): ?>
                                    <div class="attachment-preview mt-3">
                                        <img src="<?= baseUrl('uploads/tickets/' . $attachment['filename']); ?>" 
                                             alt="<?= sanitize($attachment['original_filename']); ?>"
                                             class="img-fluid rounded" style="max-height: 200px;">
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="ticket-replies">
                    <h6 class="mb-3">Replies (<?= count($replies); ?>)</h6>
                    
                    <?php if (empty($replies)): ?>
                    <div class="alert alert-info">
                        <i class="mdi mdi-information"></i>
                        No replies yet. Be the first to reply!
                    </div>
                    <?php else: ?>
                    <div class="replies-container">
                        <?php foreach ($replies as $reply): ?>
                        <div class="reply mb-3 <?= $reply['is_internal'] ? 'internal-reply' : ''; ?>">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div class="reply-author">
                                            <strong><?= sanitize($reply['user_name']); ?></strong>
                                            <span class="badge badge-<?= $reply['user_role'] === 'admin' ? 'danger' : ($reply['user_role'] === 'support' ? 'warning' : 'info'); ?>">
                                                <?= ucfirst($reply['user_role']); ?>
                                            </span>
                                            <?php if ($reply['is_internal']): ?>
                                            <span class="badge badge-secondary">Internal Note</span>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted"><?= date('M d, Y H:i', strtotime($reply['created_at'])); ?></small>
                                    </div>
                                    <div class="reply-content">
                                        <?= nl2br(sanitize($reply['message'])); ?>
                                    </div>

                                    <!-- Reply Attachments -->
                                    <?php if (isset($replyAttachments[$reply['id']]) && !empty($replyAttachments[$reply['id']])): ?>
                                    <div class="reply-attachments mt-3">
                                        <h6 class="mb-2">Attachments (<?= count($replyAttachments[$reply['id']]); ?>)</h6>
                                        <div class="attachments-grid">
                                            <?php foreach ($replyAttachments[$reply['id']] as $attachment): ?>
                                            <div class="attachment-item mb-2">
                                                <div class="card">
                                                    <div class="card-body p-2">
                                                        <div class="d-flex align-items-center">
                                                            <div class="attachment-icon mr-2">
                                                                <?php
                                                                $icon = 'mdi-file';
                                                                if (strpos($attachment['mime_type'], 'image/') === 0) {
                                                                    $icon = 'mdi-image';
                                                                } elseif (strpos($attachment['mime_type'], 'video/') === 0) {
                                                                    $icon = 'mdi-video';
                                                                } elseif (strpos($attachment['mime_type'], 'pdf') !== false) {
                                                                    $icon = 'mdi-file-pdf';
                                                                } elseif (strpos($attachment['mime_type'], 'word') !== false || strpos($attachment['mime_type'], 'document') !== false) {
                                                                    $icon = 'mdi-file-word';
                                                                }
                                                                ?>
                                                                <i class="mdi <?= $icon; ?> text-primary" style="font-size: 1.5rem;"></i>
                                                            </div>
                                                            <div class="attachment-info flex-grow-1">
                                                                <div class="font-weight-bold small"><?= sanitize($attachment['original_filename']); ?></div>
                                                                <small class="text-muted">
                                                                    <?= number_format($attachment['file_size'] / 1024 / 1024, 2); ?> MB •
                                                                    <?= date('M d, Y H:i', strtotime($attachment['created_at'])); ?>
                                                                </small>
                                                            </div>
                                                            <div class="attachment-actions">
                                                                <a href="<?= baseUrl('tickets/attachment/' . $attachment['id'] . '/download'); ?>"
                                                                   class="btn btn-sm btn-outline-primary">
                                                                    <i class="mdi mdi-download"></i> Download
                                                                </a>
                                                            </div>
                                                        </div>

                                                        <!-- Preview for images -->
                                                        <?php if (strpos($attachment['mime_type'], 'image/') === 0): ?>
                                                        <div class="attachment-preview mt-2">
                                                            <img src="<?= baseUrl('uploads/tickets/' . $attachment['filename']); ?>"
                                                                 alt="<?= sanitize($attachment['original_filename']); ?>"
                                                                 class="img-fluid rounded" style="max-height: 150px;">
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Reply Form -->
                <div class="reply-form mt-4">
                    <h6>Add Reply</h6>
                    <form method="POST" action="<?= baseUrl('tickets/' . $ticket['id'] . '/reply'); ?>" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                        
                        <div class="form-group">
                            <textarea class="form-control" name="message" rows="5" 
                                      placeholder="Type your reply here..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="reply-attachments">Attachments</label>
                            <input type="file" class="form-control-file" id="reply-attachments" name="attachments[]" multiple 
                                   accept="image/*,video/*,.pdf,.doc,.docx,.txt,.zip,.rar">
                            <small class="form-text text-muted">
                                You can attach files to your reply (Max 10MB per file)
                            </small>
                        </div>
                        
                        <?php if (in_array($user['role'], ['admin', 'support'])): ?>
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="is_internal" value="1" id="is_internal">
                                <label class="form-check-label" for="is_internal">
                                    Internal Note (Not visible to client)
                                </label>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="mdi mdi-send"></i> Send Reply
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Ticket Information</h6>
                
                <div class="ticket-info">
                    <div class="info-item mb-3">
                        <strong>Status:</strong><br>
                        <?php
                        $statusClass = '';
                        switch ($ticket['status']) {
                            case 'open':
                                $statusClass = 'badge-primary';
                                break;
                            case 'in_progress':
                                $statusClass = 'badge-warning';
                                break;
                            case 'resolved':
                                $statusClass = 'badge-success';
                                break;
                            case 'closed':
                                $statusClass = 'badge-secondary';
                                break;
                            case 'cancelled':
                                $statusClass = 'badge-danger';
                                break;
                        }
                        ?>
                        <span class="badge <?= $statusClass; ?>">
                            <?= ucfirst(str_replace('_', ' ', $ticket['status'])); ?>
                        </span>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Client:</strong><br>
                        <?= sanitize($ticket['client_name']); ?><br>
                        <small class="text-muted"><?= sanitize($ticket['client_email']); ?></small>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Assigned To:</strong><br>
                        <?= $ticket['assigned_name'] ? sanitize($ticket['assigned_name']) : '<span class="text-muted">Unassigned</span>'; ?>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Created:</strong><br>
                        <?= date('M d, Y H:i', strtotime($ticket['created_at'])); ?>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Last Updated:</strong><br>
                        <?= date('M d, Y H:i', strtotime($ticket['updated_at'])); ?>
                    </div>
                    
                    <?php if ($ticket['resolved_at']): ?>
                    <div class="info-item mb-3">
                        <strong>Resolved:</strong><br>
                        <?= date('M d, Y H:i', strtotime($ticket['resolved_at'])); ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($ticket['closed_at']): ?>
                    <div class="info-item mb-3">
                        <strong>Closed:</strong><br>
                        <?= date('M d, Y H:i', strtotime($ticket['closed_at'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Status Update Form (Admin/Support only) -->
                <?php if (in_array($user['role'], ['admin', 'support'])): ?>
                <div class="status-update mt-4">
                    <h6>Update Status</h6>
                    <form method="POST" action="<?= baseUrl('tickets/' . $ticket['id'] . '/status'); ?>">
                        <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="open" <?= $ticket['status'] === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="in_progress" <?= $ticket['status'] === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="resolved" <?= $ticket['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                <option value="closed" <?= $ticket['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                <option value="cancelled" <?= $ticket['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="assigned_to">Assign To</label>
                            <select name="assigned_to" id="assigned_to" class="form-control">
                                <option value="">Unassigned</option>
                                <?php
                                $supportUsers = DB::table('users')->whereIn('role', ['support', 'admin'])->get();
                                foreach ($supportUsers as $supportUser):
                                ?>
                                <option value="<?= $supportUser['id']; ?>" 
                                    <?= $ticket['assigned_to'] == $supportUser['id'] ? 'selected' : ''; ?>>
                                    <?= sanitize($supportUser['name']); ?> (<?= ucfirst($supportUser['role']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="mdi mdi-update"></i> Update
                            </button>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.internal-reply {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.internal-reply .card {
    background-color: #f8f9fa;
}

.reply-author {
    display: flex;
    align-items: center;
    gap: 5px;
}

.ticket-info .info-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.ticket-info .info-item:last-child {
    border-bottom: none;
}

.attachment-item .card {
    border: 1px solid #e3e6f0;
    transition: box-shadow 0.15s ease-in-out;
}

.attachment-item .card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.attachment-preview img {
    cursor: pointer;
    transition: transform 0.2s;
}

.attachment-preview img:hover {
    transform: scale(1.05);
}

.attachments-grid {
    display: grid;
    gap: 1rem;
}

.reply-attachments {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

.reply-attachments .attachment-item .card {
    border: 1px solid #dee2e6;
    background-color: #ffffff;
}

.reply-attachments .attachments-grid {
    gap: 0.5rem;
}

@media (min-width: 768px) {
    .attachments-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}
</style>