<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>User Management</h2>
                    <p class="mb-md-0">Manage system users and their roles.</p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <a href="<?= baseUrl('users/create'); ?>" class="btn btn-primary mb-2">
                    <i class="mdi mdi-plus"></i> Create New User
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="7" class="text-center">
                                    <div class="py-4">
                                        <i class="mdi mdi-account-outline h1 text-muted"></i>
                                        <p class="text-muted">No users found</p>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($users as $userItem): ?>
                            <tr>
                                <td><?= sanitize($userItem['name']); ?></td>
                                <td><?= sanitize($userItem['email']); ?></td>
                                <td>
                                    <span class="badge badge-<?= $userItem['role'] === 'admin' ? 'danger' : ($userItem['role'] === 'support' ? 'warning' : 'info'); ?>">
                                        <?= ucfirst($userItem['role']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-<?= $userItem['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                        <?= ucfirst($userItem['status']); ?>
                                    </span>
                                </td>
                                <td><?= date('M d, Y', strtotime($userItem['created_at'])); ?></td>
                                <td>
                                    <?= $userItem['last_login'] ? date('M d, Y H:i', strtotime($userItem['last_login'])) : '<span class="text-muted">Never</span>'; ?>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" 
                                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            Actions
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" href="<?= baseUrl('users/edit/' . $userItem['id']); ?>">
                                                <i class="mdi mdi-pencil"></i> Edit
                                            </a>
                                            <?php if ($userItem['id'] != $user['id']): ?>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item text-danger" href="#" 
                                               onclick="confirmDelete(<?= $userItem['id']; ?>, '<?= sanitize($userItem['name']); ?>')">
                                                <i class="mdi mdi-delete"></i> Delete
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(userId, userName) {
    document.getElementById('deleteUserName').textContent = userName;
    document.getElementById('deleteForm').action = '<?= baseUrl('users/delete/'); ?>' + userId;
    $('#deleteModal').modal('show');
}
</script>
