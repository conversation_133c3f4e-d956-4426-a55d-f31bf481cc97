<?php
/**
 * WebSocket Notification Server
 * Run this script to start the WebSocket server: php websocket-server.php
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/Config/config.php';
require_once __DIR__ . '/Helpers/functions.php';
require_once __DIR__ . '/Core/DB.php';

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer as ReactHttpServer;
use Psr\Http\Message\ServerRequestInterface;
use React\Socket\SocketServer;

class NotificationServer implements MessageComponentInterface {
    protected $clients;
    protected $userConnections;
    protected $loop;

    public function __construct($loop = null) {
        $this->clients = new \SplObjectStorage;
        $this->userConnections = [];
        $this->loop = $loop;
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "New connection! ({$conn->resourceId}) at " . date('Y-m-d H:i:s') . "\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        $data = json_decode($msg, true);

        if (!$data) {
            echo "Invalid JSON received from connection {$from->resourceId}\n";
            return;
        }

        echo "Message received from {$from->resourceId}: " . json_encode($data) . "\n";

        switch ($data['type']) {
            case 'auth':
                $this->handleAuth($from, $data);
                break;
            case 'ping':
                $from->send(json_encode(['type' => 'pong']));
                break;
            default:
                echo "Unknown message type: {$data['type']}\n";
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);

        // Remove from user connections
        $disconnectedUserId = null;
        foreach ($this->userConnections as $userId => $connections) {
            if (($key = array_search($conn, $connections)) !== false) {
                unset($this->userConnections[$userId][$key]);
                if (empty($this->userConnections[$userId])) {
                    unset($this->userConnections[$userId]);
                }
                $disconnectedUserId = $userId;
                break;
            }
        }

        echo "Connection {$conn->resourceId} has disconnected";
        if ($disconnectedUserId) {
            echo " (User ID: {$disconnectedUserId})";
        }
        echo " at " . date('Y-m-d H:i:s') . "\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred on connection {$conn->resourceId}: {$e->getMessage()}\n";
        $conn->close();
    }

    private function handleAuth(ConnectionInterface $conn, $data) {
        if (!isset($data['user_id']) || !isset($data['token'])) {
            echo "Authentication failed: missing user_id or token\n";
            $conn->send(json_encode([
                'type' => 'auth_error',
                'message' => 'Missing user_id or token'
            ]));
            return;
        }

        $userId = $data['user_id'];
        $token = $data['token'];

        // Verify token
        if ($this->verifyToken($userId, $token)) {
            if (!isset($this->userConnections[$userId])) {
                $this->userConnections[$userId] = [];
            }
            $this->userConnections[$userId][] = $conn;

            $conn->send(json_encode([
                'type' => 'auth_success',
                'message' => 'Authentication successful',
                'user_id' => $userId
            ]));

            echo "User {$userId} authenticated successfully at " . date('Y-m-d H:i:s') . "\n";
        } else {
            echo "Authentication failed for user {$userId}: invalid token\n";
            $conn->send(json_encode([
                'type' => 'auth_error',
                'message' => 'Invalid token'
            ]));
        }
    }

    private function verifyToken($userId, $token) {
        // Simple token verification - implement proper JWT or session verification
        $expectedToken = hash('sha256', $userId . 'secret_key');
        return $expectedToken === $token;
    }

    public function sendNotificationToUser($userId, $notification) {
        if (isset($this->userConnections[$userId])) {
            $message = json_encode([
                'type' => 'notification',
                'data' => $notification
            ]);

            $sentCount = 0;
            foreach ($this->userConnections[$userId] as $conn) {
                try {
                    $conn->send($message);
                    $sentCount++;
                } catch (\Exception $e) {
                    echo "Failed to send notification to user {$userId}: {$e->getMessage()}\n";
                }
            }

            echo "Sent notification to user {$userId} ({$sentCount} connections) at " . date('Y-m-d H:i:s') . "\n";
            return $sentCount > 0;
        } else {
            echo "User {$userId} not connected for notification\n";
            return false;
        }
    }

    public function sendNotificationToRole($role, $notification) {
        echo "Sending notification to role: {$role}\n";

        // Get all users with the specified role
        try {
            $users = DB::table('users')->where('role', $role)->where('status', 'active')->get();
            $sentCount = 0;

            foreach ($users as $user) {
                if ($this->sendNotificationToUser($user['id'], $notification)) {
                    $sentCount++;
                }
            }

            echo "Notification sent to {$sentCount} users with role {$role}\n";
            return $sentCount;
        } catch (\Exception $e) {
            echo "Error sending notification to role {$role}: {$e->getMessage()}\n";
            return 0;
        }
    }

    public function broadcastNotification($notification) {
        $message = json_encode([
            'type' => 'notification',
            'data' => $notification
        ]);

        $sentCount = 0;
        foreach ($this->clients as $client) {
            try {
                $client->send($message);
                $sentCount++;
            } catch (\Exception $e) {
                echo "Failed to broadcast notification: {$e->getMessage()}\n";
            }
        }

        echo "Broadcast notification to {$sentCount} clients at " . date('Y-m-d H:i:s') . "\n";
        return $sentCount;
    }

    public function getConnectedUsers() {
        return array_keys($this->userConnections);
    }

    public function getConnectionCount() {
        return count($this->clients);
    }
}

// Create event loop
$loop = Loop::get();

// Create notification server with loop reference
$notificationServer = new NotificationServer($loop);

// HTTP server for notification POSTs (port 8081)
$httpServer = new ReactHttpServer(function (ServerRequestInterface $request) use ($notificationServer) {
    $path = $request->getUri()->getPath();
    $method = $request->getMethod();

    echo "HTTP Request: {$method} {$path} at " . date('Y-m-d H:i:s') . "\n";

    if ($method === 'POST' && $path === '/notify') {
        $body = $request->getBody()->getContents();
        $data = json_decode($body, true);

        if (!$data) {
            echo "Invalid JSON received in HTTP request\n";
            return new \React\Http\Message\Response(
                400,
                ['Content-Type' => 'application/json'],
                json_encode(['success' => false, 'error' => 'Invalid JSON'])
            );
        }

        echo "Notification request: " . json_encode($data) . "\n";

        // Handle notification actions
        $result = false;
        if ($data['action'] === 'send_to_user' && isset($data['user_id'], $data['notification'])) {
            $result = $notificationServer->sendNotificationToUser($data['user_id'], $data['notification']);
        } elseif ($data['action'] === 'send_to_role' && isset($data['role'], $data['notification'])) {
            $result = $notificationServer->sendNotificationToRole($data['role'], $data['notification']);
        } elseif ($data['action'] === 'broadcast' && isset($data['notification'])) {
            $result = $notificationServer->broadcastNotification($data['notification']);
        } else {
            echo "Unknown action or missing parameters\n";
        }

        return new \React\Http\Message\Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode(['success' => true, 'sent' => $result])
        );
    }

    if ($method === 'GET' && $path === '/status') {
        return new \React\Http\Message\Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode([
                'success' => true,
                'connected_users' => $notificationServer->getConnectedUsers(),
                'total_connections' => $notificationServer->getConnectionCount(),
                'timestamp' => date('Y-m-d H:i:s')
            ])
        );
    }

    return new \React\Http\Message\Response(
        404,
        ['Content-Type' => 'application/json'],
        json_encode(['success' => false, 'error' => 'Not found'])
    );
});

// Listen on port 8081 for HTTP notification POSTs
$httpSocket = new SocketServer('0.0.0.0:8081', [], $loop);
$httpServer->listen($httpSocket);

// WebSocket server (port 8080)
$wsSocket = new SocketServer('0.0.0.0:8080', [], $loop);
$wsServer = new IoServer(
    new HttpServer(
        new WsServer(
            $notificationServer
        )
    ),
    $wsSocket,
    $loop
);

echo "WebSocket server started on port 8080\n";
echo "HTTP notification endpoint started on port 8081\n";
echo "Server status endpoint available at http://localhost:8081/status\n";
echo "Starting servers at " . date('Y-m-d H:i:s') . "\n";

// Run the event loop
$loop->run();