<?php
/**
 * Footer layout
 */

// Ensure functions are available
if (!function_exists('base_url')) {
    require_once __DIR__ . '/../../Helpers/functions.php';
}

$authenticated = function_exists('isLoggedIn') ? isLoggedIn() : (isset($isLoggedIn) && $isLoggedIn);
?>

<?php if ($authenticated): ?>
                    </div>
                    <!-- content-wrapper ends -->

                    <!-- footer -->
                    <footer class="footer">
                        <div class="footer-inner-wraper">
                            <div class="d-sm-flex justify-content-center justify-content-sm-between">
                                <span class="text-muted d-block text-center text-sm-left d-sm-inline-block">Copyright © bootstrapdash.com 2020</span>
                                <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center"> Free <a href="https://www.bootstrapdash.com/" target="_blank">Bootstrap dashboard templates</a> from Bootstrapdash.com</span>
                            </div>
                        </div>
                    </footer>
                    <!-- footer ends -->
                </div>
                <!-- main-panel ends -->
            </div>
            <!-- page-body-wrapper ends -->
<?php else: ?>
                        </div>
                    </div>
                </div>
            </div>
            <!-- page-body-wrapper ends -->
<?php endif; ?>
    </div>
    <!-- container-scroller ends -->

    <!-- plugins:js -->
    <script src="<?= function_exists('asset_url') ? asset_url('vendors/js/vendor.bundle.base.js') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/js/vendor.bundle.base.js' : ''); ?>"></script>

    <!-- DataTables JS -->
    <script src="<?= function_exists('asset_url') ? asset_url('vendors/datatables/dataTables.min.js') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/datatables/dataTables.min.js' : ''); ?>"></script>
    <script src="<?= function_exists('asset_url') ? asset_url('vendors/datatables/dataTables.bootstrap4.min.js') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/datatables/dataTables.bootstrap4.min.js' : ''); ?>"></script>

    <!-- Chart.js -->
    <script src="<?= function_exists('asset_url') ? asset_url('vendors/chart.js/Chart.min.js') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/chart.js/Chart.min.js' : ''); ?>"></script>

    <!-- Custom js -->
    <script src="<?= function_exists('asset_url') ? asset_url('js/off-canvas.js') : (isset($baseUrl) ? $baseUrl . 'assets/js/off-canvas.js' : ''); ?>"></script>
    <script src="<?= function_exists('asset_url') ? asset_url('js/hoverable-collapse.js') : (isset($baseUrl) ? $baseUrl . 'assets/js/hoverable-collapse.js' : ''); ?>"></script>
    <script src="<?= function_exists('asset_url') ? asset_url('js/misc.js') : (isset($baseUrl) ? $baseUrl . 'assets/js/misc.js' : ''); ?>"></script>
    <script src="<?= function_exists('asset_url') ? asset_url('js/chart.js') : (isset($baseUrl) ? $baseUrl . 'assets/js/chart.js' : ''); ?>"></script>

    <!-- Flash message auto-dismiss -->
    <script>
        $(document).ready(function() {
            // Auto-dismiss flash messages after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // Ensure Bootstrap alert functionality works
            $('.alert .close').on('click', function() {
                $(this).parent().fadeOut('fast');
            });
        });
    </script>
    <script src="<?= function_exists('asset_url') ? asset_url('js/notification-client.js') : (isset($baseUrl) ? $baseUrl . 'assets/js/notification-client.js' : '/assets/js/notification-client.js'); ?>"></script>
</body>
</html>
