<?php
/**
 * Header layout
 */

// Ensure functions are available
if (!function_exists('base_url')) {
    require_once __DIR__ . '/../../Helpers/functions.php';
}

// Ensure config is available (fallback)
if (!isset($config)) {
    $config = [
        'site_title' => 'Ticketing App',
        'debug_mode' => true
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="base-url" content="<?= function_exists('base_url') ? base_url() : (isset($baseUrl) ? $baseUrl : ''); ?>">
    <title><?= isset($title) ? $title . ' - ' . (isset($config['site_title']) ? $config['site_title'] : 'Ticketing App') : (isset($config['site_title']) ? $config['site_title'] : 'Ticketing App'); ?></title>

    <!-- plugins:css -->
    <link rel="stylesheet" href="<?= function_exists('asset_url') ? asset_url('vendors/mdi/css/materialdesignicons.min.css') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/mdi/css/materialdesignicons.min.css' : ''); ?>">
    <link rel="stylesheet" href="<?= function_exists('asset_url') ? asset_url('vendors/flag-icon-css/css/flag-icon.min.css') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/flag-icon-css/css/flag-icon.min.css' : ''); ?>">
    <link rel="stylesheet" href="<?= function_exists('asset_url') ? asset_url('vendors/css/vendor.bundle.base.css') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/css/vendor.bundle.base.css' : ''); ?>">

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="<?= function_exists('asset_url') ? asset_url('vendors/datatables/dataTables.bootstrap4.min.css') : (isset($baseUrl) ? $baseUrl . 'assets/vendors/datatables/dataTables.bootstrap4.min.css' : ''); ?>">

    <!-- Layout styles -->
    <link rel="stylesheet" href="<?= function_exists('asset_url') ? asset_url('css/style.css') : (isset($baseUrl) ? $baseUrl . 'assets/css/style.css' : ''); ?>">

    <!-- Notification UI Styles -->
    <style>
        .notification-dropdown {
            position: relative;
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            min-width: 16px;
            text-align: center;
            display: none;
        }

        .notification-dropdown-menu {
            width: 350px;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .notification-content h6 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .notification-content p {
            margin: 0 0 4px 0;
            font-size: 13px;
            color: #666;
        }

        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-notification {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
            transition: opacity 0.3s ease-out;
        }

        .toast-notification.toast-info {
            border-left: 4px solid #17a2b8;
        }

        .toast-notification.toast-success {
            border-left: 4px solid #28a745;
        }

        .toast-notification.toast-warning {
            border-left: 4px solid #ffc107;
        }

        .toast-notification.toast-primary {
            border-left: 4px solid #007bff;
        }

        .toast-notification.fade-out {
            opacity: 0;
            transform: translateX(100%);
        }

        .toast-header {
            padding: 8px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toast-body {
            padding: 12px;
            font-size: 14px;
        }

        .toast-actions {
            margin-top: 8px;
        }

        .toast-actions .btn {
            font-size: 12px;
            padding: 4px 8px;
        }

        .close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            margin-left: auto;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>

    <!-- Custom CSS for flash messages -->
    <style>
        .alert {
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.25rem;
        }
        
        .alert-dismissible .close {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0.75rem 1.25rem;
            color: inherit;
            background-color: transparent;
            border: 0;
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            text-shadow: 0 1px 0 #fff;
            opacity: 0.5;
        }
        
        .alert-dismissible .close:hover {
            color: inherit;
            text-decoration: none;
            opacity: 0.75;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?= function_exists('asset_url') ? asset_url('images/fav-32x32.png') : (isset($baseUrl) ? $baseUrl . 'assets/images/fav-32x32.png' : ''); ?>" />
</head>
<body class="sidebar-fixed">
    <div class="container-scroller">
        <?php 
        // Ensure session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $authenticated = function_exists('isLoggedIn') ? isLoggedIn() : (isset($isLoggedIn) && $isLoggedIn);
        ?>
        <?php if ($authenticated): ?>
            <!-- Include navbar -->
            <?php include_once dirname(dirname(__DIR__)) . '/Views/partials/navbar.php'; ?>

            <div class="container-fluid page-body-wrapper">
                <!-- Include sidebar -->
                <?php include_once dirname(dirname(__DIR__)) . '/Views/partials/sidebar.php'; ?>

                <div class="main-panel">
                    <div class="content-wrapper">
                        <!-- Flash messages -->
                        <?php if (isset($flashes) && !empty($flashes)): ?>
                            <div class="row">
                                <div class="col-12">
                                    <?php foreach ($flashes as $flash): ?>
                                        <div class="alert alert-<?= htmlspecialchars($flash['type']); ?> alert-dismissible fade show" role="alert">
                                            <strong><?= ucfirst($flash['type']); ?>:</strong> <?= htmlspecialchars($flash['message']); ?>
                                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Main content will be inserted here -->
        <?php else: ?>
            <!-- Login page layout -->
            <div class="container-fluid page-body-wrapper full-page-wrapper">
                <div class="content-wrapper d-flex align-items-center auth">
                    <div class="row w-100">
                        <div class="col-lg-4 mx-auto">
                            <!-- Flash messages for login page -->
                            <?php if (isset($flashes) && !empty($flashes)): ?>
                                <?php foreach ($flashes as $flash): ?>
                                    <div class="alert alert-<?= htmlspecialchars($flash['type']); ?> alert-dismissible fade show" role="alert">
                                        <strong><?= ucfirst($flash['type']); ?>:</strong> <?= htmlspecialchars($flash['message']); ?>
                                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <!-- Login content will be inserted here -->
        <?php endif; ?>
