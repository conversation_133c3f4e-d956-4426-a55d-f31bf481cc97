<?php
/**
 * Notification Controller
 * Handles notification-related requests
 */


require_once __DIR__ . '/../Core/Controller.php';
require_once __DIR__ . '/../Notifications/NotificationManager.php';

class NotificationController extends Controller {
    
    /**
     * Get user notifications (AJAX)
     */
    public function index() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        $limit = (int) $this->getGet('limit', 20);
        $unreadOnly = $this->getGet('unread_only') === '1';
        
        $notificationManager = NotificationManager::getInstance();
        $notifications = $notificationManager->getUserNotifications($user['id'], $limit, $unreadOnly);
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'notifications' => $notifications
        ]);
    }
    
    /**
     * Mark notification as read
     */
    public function markAsRead($id) {
        $this->requireAuth();
        
        try {
            $this->validateCSRF();
            
            $user = getCurrentUser();
            $notificationManager = NotificationManager::getInstance();
            $notificationManager->markAsRead($id, $user['id']);
            
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
            
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Mark all notifications as read
     */
    public function markAllAsRead() {
        $this->requireAuth();
        
        try {
            $this->validateCSRF();
            
            $user = getCurrentUser();
            $notificationManager = NotificationManager::getInstance();
            $notificationManager->markAllAsRead($user['id']);
            
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
            
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get unread notification count
     */
    public function getUnreadCount() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        $count = DB::table('notifications')
            ->where('user_id', $user['id'])
            ->where('is_read', 0)
            ->count();
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'count' => $count
        ]);
    }
    
    /**
     * Generate auth token for WebSocket
     */
    public function getWebSocketToken() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        $token = hash('sha256', $user['id'] . 'secret_key');
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'token' => $token,
            'user_id' => $user['id']
        ]);
    }
}