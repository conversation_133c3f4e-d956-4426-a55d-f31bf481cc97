<?php
/**
 * Notification Manager
 * Handles creating and sending notifications
 */

class NotificationManager {
    private static $instance = null;
    private $wsClient = null;

    private function __construct() {
        // Initialize WebSocket client for sending notifications
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Create a notification in the database
     */
    public function createNotification($userId, $type, $title, $message, $data = []) {
        $notificationId = DB::table('notifications')->insert([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $notification = [
            'id' => $notificationId,
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'is_read' => false,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Send real-time notification
        $this->sendRealTimeNotification($userId, $notification);

        return $notificationId;
    }

    /**
     * Send notification to WebSocket server
     */
    private function sendRealTimeNotification($userId, $notification) {
        // Send to WebSocket server via HTTP API or direct connection
        $this->sendToWebSocketServer([
            'action' => 'send_to_user',
            'user_id' => $userId,
            'notification' => $notification
        ]);
    }

    /**
     * Send notification to all users with specific role
     */
    public function notifyRole($role, $type, $title, $message, $data = []) {
        $users = DB::table('users')->where('role', $role)->where('status', 'active')->get();

        foreach ($users as $user) {
            $this->createNotification($user['id'], $type, $title, $message, $data);
        }

        // Also send real-time notification to all users with this role
        $notification = [
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'is_read' => false,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->sendRoleNotification($role, $notification);
    }

    /**
     * Ticket-specific notification methods
     */
    public function notifyNewTicket($ticketId, $clientId) {
        $ticket = DB::table('tickets as t')
            ->join('users as u', 't.client_id', '=', 'u.id')
            ->select(['t.*', 'u.name as client_name'])
            ->where('t.id', $ticketId)
            ->first();

        if (!$ticket) return;

        $title = 'New Ticket Created';
        $message = "New ticket #{$ticket['ticket_number']} created by {$ticket['client_name']}";
        $data = [
            'ticket_id' => $ticketId,
            'ticket_number' => $ticket['ticket_number'],
            'client_name' => $ticket['client_name'],
            'subject' => $ticket['subject']
        ];

        // Notify all admins and support agents
        $this->notifyRole('admin', 'new_ticket', $title, $message, $data);
        $this->notifyRole('support', 'new_ticket', $title, $message, $data);
    }

    public function notifyTicketStatusChange($ticketId, $oldStatus, $newStatus, $updatedBy) {
        $ticket = DB::table('tickets as t')
            ->join('users as u', 't.client_id', '=', 'u.id')
            ->select(['t.*', 'u.name as client_name'])
            ->where('t.id', $ticketId)
            ->first();

        if (!$ticket) return;

        $updater = DB::table('users')->where('id', $updatedBy)->first();
        
        $title = 'Ticket Status Updated';
        $message = "Ticket #{$ticket['ticket_number']} status changed from {$oldStatus} to {$newStatus} by {$updater['name']}";
        $data = [
            'ticket_id' => $ticketId,
            'ticket_number' => $ticket['ticket_number'],
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'updated_by' => $updater['name']
        ];

        // Notify the client who owns the ticket
        $this->createNotification($ticket['client_id'], 'ticket_status_change', $title, $message, $data);
    }

    public function notifyTicketReply($ticketId, $replyId, $userId) {
        $ticket = DB::table('tickets as t')
            ->join('users as u', 't.client_id', '=', 'u.id')
            ->select(['t.*', 'u.name as client_name'])
            ->where('t.id', $ticketId)
            ->first();

        if (!$ticket) return;

        $replier = DB::table('users')->where('id', $userId)->first();
        $reply = DB::table('ticket_replies')->where('id', $replyId)->first();

        $title = 'New Reply on Ticket';
        $message = "New reply on ticket #{$ticket['ticket_number']} by {$replier['name']}";
        $data = [
            'ticket_id' => $ticketId,
            'ticket_number' => $ticket['ticket_number'],
            'reply_id' => $replyId,
            'replier_name' => $replier['name'],
            'replier_role' => $replier['role']
        ];

        if ($replier['role'] === 'client') {
            // Client replied - notify admins and support
            $this->notifyRole('admin', 'ticket_reply', $title, $message, $data);
            $this->notifyRole('support', 'ticket_reply', $title, $message, $data);
        } else {
            // Admin/Support replied - notify client (if not internal note)
            if (!$reply['is_internal']) {
                $this->createNotification($ticket['client_id'], 'ticket_reply', $title, $message, $data);
            }
        }
    }

    public function notifyTicketAssignment($ticketId, $assignedTo, $assignedBy) {
        $ticket = DB::table('tickets')->where('id', $ticketId)->first();
        $assignee = DB::table('users')->where('id', $assignedTo)->first();
        $assigner = DB::table('users')->where('id', $assignedBy)->first();

        if (!$ticket || !$assignee) return;

        $title = 'Ticket Assigned';
        $message = "Ticket #{$ticket['ticket_number']} has been assigned to you by {$assigner['name']}";
        $data = [
            'ticket_id' => $ticketId,
            'ticket_number' => $ticket['ticket_number'],
            'assigned_by' => $assigner['name']
        ];

        $this->createNotification($assignedTo, 'ticket_assignment', $title, $message, $data);
    }

    /**
     * Get user notifications
     */
    public function getUserNotifications($userId, $limit = 20, $unreadOnly = false) {
        $query = DB::table('notifications')
            ->where('user_id', $userId);

        if ($unreadOnly) {
            $query->where('is_read', 0);
        }

        return $query->orderBy('created_at', 'DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId) {
        DB::update('notifications', [
            'is_read' => 1,
            'read_at' => date('Y-m-d H:i:s')
        ], [
            'id' => $notificationId,
            'user_id' => $userId
        ]);
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead($userId) {
        DB::update('notifications', [
            'is_read' => 1,
            'read_at' => date('Y-m-d H:i:s')
        ], [
            'user_id' => $userId,
            'is_read' => 0
        ]);
    }

    /**
     * Send role-based notification via WebSocket
     */
    private function sendRoleNotification($role, $notification) {
        $this->sendToWebSocketServer([
            'action' => 'send_to_role',
            'role' => $role,
            'notification' => $notification
        ]);
    }

    /**
     * Send data to WebSocket server
     */
    private function sendToWebSocketServer($data) {
        try {
            $ch = curl_init('http://localhost:8081/notify');
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5); // 5 second timeout
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2); // 2 second connection timeout

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                error_log("WebSocket notification failed: " . $error);
                return false;
            }

            if ($httpCode !== 200) {
                error_log("WebSocket notification failed with HTTP code: " . $httpCode);
                return false;
            }

            $result = json_decode($response, true);
            if (!$result || !$result['success']) {
                error_log("WebSocket notification failed: " . $response);
                return false;
            }

            return true;

        } catch (Exception $e) {
            error_log("WebSocket notification exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if WebSocket server is running
     */
    public function isWebSocketServerRunning() {
        try {
            $ch = curl_init('http://localhost:8081/status');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 2);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return $httpCode === 200;
        } catch (Exception $e) {
            return false;
        }
    }
}