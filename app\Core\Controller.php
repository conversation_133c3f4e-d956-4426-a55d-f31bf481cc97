<?php
/**
 * Base Controller class
 */

class Controller {
    protected $db;
    protected $request;
    protected $response;
    
    public function __construct() {
        $this->db = new DB();
        $this->request = $_REQUEST;
        $this->response = [];
    }
    
    /**
     * Render a view
     */
    protected function render($view, $data = [], $layout = 'main') {
        // Load config if not already loaded
        global $config;
        if (!isset($config)) {
            require_once __DIR__ . '/../Config/config.php';
        }
        
        // Add common data to all views
        $data['user'] = getCurrentUser();
        $data['isLoggedIn'] = isLoggedIn();
        $data['currentUrl'] = currentUrl();
        $data['baseUrl'] = baseUrl();
        $data['csrfToken'] = generateCSRFToken();
        $data['config'] = $config; // Add config to all views
        
        // Add flash messages - use get_flashes() to get all at once
        $flashMessages = get_flashes();
        $data['flashes'] = $flashMessages ?: [];
        
        // Start output buffering
        ob_start();
        
        // Include the view
        $viewPath = __DIR__ . '/../Views/' . $view . '.php';
        if (file_exists($viewPath)) {
            extract($data);
            include $viewPath;
        } else {
            throw new Exception("View not found: $view");
        }
        
        // Get the content
        $content = ob_get_clean();
        
        // Include layout if specified
        if ($layout) {
            $layoutPath = __DIR__ . '/../Views/layouts/' . $layout . '.php';
            if (file_exists($layoutPath)) {
                extract($data);
                include $layoutPath;
            } else {
                echo $content;
            }
        } else {
            echo $content;
        }
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Redirect to URL
     */
    protected function redirect($url) {
        header("Location: $url");
        exit;
    }
    
    /**
     * Get request method
     */
    protected function getMethod() {
        return $_SERVER['REQUEST_METHOD'];
    }
    
    /**
     * Check if request is POST
     */
    protected function isPost() {
        return $this->getMethod() === 'POST';
    }
    
    /**
     * Check if request is GET
     */
    protected function isGet() {
        return $this->getMethod() === 'GET';
    }
    
    /**
     * Get POST data
     */
    protected function getPost($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        return $_POST[$key] ?? $default;
    }
    
    /**
     * Get GET data
     */
    protected function getGet($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        return $_GET[$key] ?? $default;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF() {
        if (!$this->isPost()) {
            return true;
        }
        
        $token = $this->getPost('csrf_token');
        if (!verifyCSRFToken($token)) {
            throw new Exception('Invalid CSRF token');
        }
        
        return true;
    }
    
    /**
     * Require authentication
     */
    protected function requireAuth() {
        if (!isLoggedIn()) {
            $this->redirect(baseUrl('login'));
        }
    }
    
    /**
     * Require specific role
     */
    protected function requireRole($role) {
        $this->requireAuth();
        if (!hasRole($role)) {
            $this->redirect(baseUrl('unauthorized'));
        }
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($type, $message) {
        setFlash($type, $message);
    }
    
    /**
     * Before action hook
     */
    public function before() {
        // Override in child controllers
    }
    
    /**
     * After action hook
     */
    public function after() {
        // Override in child controllers
    }
}
