<?php
/**
 * Create Priority Page
 */
?>

<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
    <div>
        <h4 class="mb-3 mb-md-0">Create New Priority</h4>
    </div>
    <div class="d-flex align-items-center flex-wrap text-nowrap">
        <a href="<?= baseUrl('priorities'); ?>" class="btn btn-outline-primary btn-icon-text mb-2 mb-md-0">
            <i class="btn-icon-prepend" data-feather="arrow-left"></i>
            Back to Priorities
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Priority Information</h6>
                
                <form method="POST" action="<?= baseUrl('priorities'); ?>">
                    <?= csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Priority Name *</label>
                                <input type="text" name="name" id="name" class="form-control" 
                                       value="<?= old('name'); ?>" required maxlength="50">
                                <small class="form-text text-muted">Enter a unique name for the priority</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="level">Priority Level *</label>
                                <input type="number" name="level" id="level" class="form-control" 
                                       value="<?= old('level'); ?>" required min="1" max="100">
                                <small class="form-text text-muted">Lower numbers = higher priority (1 = highest)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="color">Color *</label>
                                <input type="color" name="color" id="color" class="form-control" 
                                       value="<?= old('color', '#007bff'); ?>" required>
                                <small class="form-text text-muted">Choose a color to represent this priority</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">Status *</label>
                                <select name="status" id="status" class="form-control" required>
                                    <option value="active" <?= old('status') === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                                <small class="form-text text-muted">Only active priorities will be available for ticket creation</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Preview</label>
                        <div class="border rounded p-3">
                            <span id="priorityPreview" class="badge" style="background-color: #007bff; color: white;">
                                Priority Preview
                            </span>
                        </div>
                        <small class="form-text text-muted">This is how the priority will appear in the system</small>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i data-feather="save" class="icon-sm mr-2"></i>
                            Create Priority
                        </button>
                        <a href="<?= baseUrl('priorities'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Guidelines</h6>
                <div class="alert alert-info">
                    <h6 class="alert-heading">Priority Best Practices</h6>
                    <ul class="mb-0">
                        <li>Use clear, descriptive names</li>
                        <li>Keep level numbers logical (1 = highest)</li>
                        <li>Choose distinct colors for easy identification</li>
                        <li>Consider your team's workflow needs</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">Note</h6>
                    <p class="mb-0">Priority names and levels must be unique. Choose colors that provide good contrast and are accessible.</p>
                </div>
                
                <div class="alert alert-secondary">
                    <h6 class="alert-heading">Common Priorities</h6>
                    <ul class="mb-0">
                        <li>Critical (Level 1) - Red</li>
                        <li>High (Level 2) - Orange</li>
                        <li>Medium (Level 3) - Yellow</li>
                        <li>Low (Level 4) - Green</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const preview = document.getElementById('priorityPreview');
    
    function updatePreview() {
        const name = nameInput.value || 'Priority Preview';
        const color = colorInput.value;
        
        preview.textContent = name;
        preview.style.backgroundColor = color;
        preview.style.color = 'white';
    }
    
    nameInput.addEventListener('input', updatePreview);
    colorInput.addEventListener('input', updatePreview);
    
    // Initial preview update
    updatePreview();
});
</script>
