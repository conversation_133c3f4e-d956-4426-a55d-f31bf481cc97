# Apache configuration for Ticketing App
RewriteEngine On

# Set the base directory
RewriteBase /

# Redirect to HTTPS (uncomment in production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Handle front controller pattern
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security: Prevent access to sensitive files
<FilesMatch "\.(sql|log|md|json|lock|yml|yaml|dockerignore|gitignore)$">
    Require all denied
</FilesMatch>
