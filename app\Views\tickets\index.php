<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2><?= $user['role'] === 'client' ? 'My Tickets' : 'Tickets'; ?></h2>
                    <p class="mb-md-0">Manage and track support tickets.</p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <?php if ($user['role'] === 'client'): ?>
                <a href="<?= baseUrl('tickets/create'); ?>" class="btn btn-primary mb-2">
                    <i class="mdi mdi-plus"></i> Create New Ticket
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="card">
            <div class="card-body">
                <form method="GET" action="<?= baseUrl('tickets'); ?>">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="open" <?= $currentFilters['status'] === 'open' ? 'selected' : ''; ?>>Open</option>
                                    <option value="in_progress" <?= $currentFilters['status'] === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                    <option value="resolved" <?= $currentFilters['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                    <option value="closed" <?= $currentFilters['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                    <option value="cancelled" <?= $currentFilters['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="priority">Priority</label>
                                <select name="priority" id="priority" class="form-control">
                                    <option value="">All Priorities</option>
                                    <?php foreach ($priorities as $priority): ?>
                                    <option value="<?= $priority['id']; ?>" 
                                        <?= $currentFilters['priority'] == $priority['id'] ? 'selected' : ''; ?>>
                                        <?= $priority['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <?php if ($user['role'] === 'support'): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter">Filter</label>
                                <select name="filter" id="filter" class="form-control">
                                    <option value="all" <?= $this->getGet('filter', 'all') === 'all' ? 'selected' : ''; ?>>All Tickets</option>
                                    <option value="assigned" <?= $this->getGet('filter') === 'assigned' ? 'selected' : ''; ?>>Assigned to Me</option>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                    <a href="<?= baseUrl('tickets'); ?>" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tickets Table -->
<div class="row">
    <div class="col-md-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Subject</th>
                                <?php if ($user['role'] !== 'client'): ?>
                                <th>Client</th>
                                <?php endif; ?>
                                <th>Category</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <?php if ($user['role'] !== 'client'): ?>
                                <th>Assigned To</th>
                                <?php endif; ?>
                                <th>Created</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($tickets)): ?>
                            <tr>
                                <td colspan="<?= $user['role'] === 'client' ? '6' : '8'; ?>" class="text-center">
                                    <div class="py-4">
                                        <i class="mdi mdi-file-outline h1 text-muted"></i>
                                        <p class="text-muted">No tickets found</p>
                                        <?php if ($user['role'] === 'client'): ?>
                                        <a href="<?= baseUrl('tickets/create'); ?>" class="btn btn-primary">
                                            Create Your First Ticket
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($tickets as $ticket): ?>
                            <tr>
                                <td>
                                    <a href="<?= baseUrl('tickets/' . $ticket['id']); ?>" class="text-primary">
                                        #<?= $ticket['ticket_number']; ?>
                                    </a>
                                </td>
                                <td>
                                    <a href="<?= baseUrl('tickets/' . $ticket['id']); ?>" class="text-dark">
                                        <?= sanitize($ticket['subject']); ?>
                                    </a>
                                </td>
                                <?php if ($user['role'] !== 'client'): ?>
                                <td><?= sanitize($ticket['client_name']); ?></td>
                                <?php endif; ?>
                                <td>
                                    <span class="badge badge-info"><?= sanitize($ticket['category_name']); ?></span>
                                </td>
                                <td>
                                    <span class="badge" style="background-color: <?= $ticket['priority_color']; ?>; color: white;">
                                        <?= sanitize($ticket['priority_name']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    switch ($ticket['status']) {
                                        case 'open':
                                            $statusClass = 'badge-primary';
                                            break;
                                        case 'in_progress':
                                            $statusClass = 'badge-warning';
                                            break;
                                        case 'resolved':
                                            $statusClass = 'badge-success';
                                            break;
                                        case 'closed':
                                            $statusClass = 'badge-secondary';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'badge-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass; ?>">
                                        <?= ucfirst(str_replace('_', ' ', $ticket['status'])); ?>
                                    </span>
                                </td>
                                <?php if ($user['role'] !== 'client'): ?>
                                <td>
                                    <?= $ticket['assigned_name'] ? sanitize($ticket['assigned_name']) : '<span class="text-muted">Unassigned</span>'; ?>
                                </td>
                                <?php endif; ?>
                                <td><?= date('M d, Y', strtotime($ticket['created_at'])); ?></td>
                                <td>
                                    <a href="<?= baseUrl('tickets/' . $ticket['id']); ?>" class="btn btn-primary btn-sm">
                                        <i class="mdi mdi-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
