<?php
/**
 * Edit Priority Page
 */
?>

<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
    <div>
        <h4 class="mb-3 mb-md-0">Edit Priority</h4>
    </div>
    <div class="d-flex align-items-center flex-wrap text-nowrap">
        <a href="<?= baseUrl('priorities'); ?>" class="btn btn-outline-primary btn-icon-text mb-2 mb-md-0">
            <i class="btn-icon-prepend" data-feather="arrow-left"></i>
            Back to Priorities
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Priority Information</h6>
                
                <form method="POST" action="<?= baseUrl('priorities/update/' . $priority['id']); ?>">
                    <?= csrf_field(); ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Priority Name *</label>
                                <input type="text" name="name" id="name" class="form-control" 
                                       value="<?= old('name', $priority['name']); ?>" required maxlength="50">
                                <small class="form-text text-muted">Enter a unique name for the priority</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="level">Priority Level *</label>
                                <input type="number" name="level" id="level" class="form-control" 
                                       value="<?= old('level', $priority['level']); ?>" required min="1" max="100">
                                <small class="form-text text-muted">Lower numbers = higher priority (1 = highest)</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="color">Color *</label>
                                <input type="color" name="color" id="color" class="form-control" 
                                       value="<?= old('color', $priority['color']); ?>" required>
                                <small class="form-text text-muted">Choose a color to represent this priority</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="status">Status *</label>
                                <select name="status" id="status" class="form-control" required>
                                    <option value="active" <?= old('status', $priority['status']) === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?= old('status', $priority['status']) === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                                <small class="form-text text-muted">Only active priorities will be available for ticket creation</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Preview</label>
                        <div class="border rounded p-3">
                            <span id="priorityPreview" class="badge" style="background-color: <?= $priority['color']; ?>; color: white;">
                                <?= sanitize($priority['name']); ?>
                            </span>
                        </div>
                        <small class="form-text text-muted">This is how the priority will appear in the system</small>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i data-feather="save" class="icon-sm mr-2"></i>
                            Update Priority
                        </button>
                        <a href="<?= baseUrl('priorities'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Priority Details</h6>
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Current Level:</strong></td>
                            <td><span class="badge badge-light"><?= $priority['level']; ?></span></td>
                        </tr>
                        <tr>
                            <td><strong>Current Color:</strong></td>
                            <td>
                                <code><?= sanitize($priority['color']); ?></code>
                                <span class="badge ml-2" style="background-color: <?= $priority['color']; ?>; color: white;">
                                    Sample
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td><?= date('M j, Y g:i A', strtotime($priority['created_at'])); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Last Updated:</strong></td>
                            <td><?= date('M j, Y g:i A', strtotime($priority['updated_at'])); ?></td>
                        </tr>
                        <tr>
                            <td><strong>Current Status:</strong></td>
                            <td>
                                <?php if ($priority['status'] === 'active'): ?>
                                    <span class="badge badge-success">Active</span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">Important</h6>
                    <p class="mb-0">Changing the priority status to "Inactive" will hide it from ticket creation forms, but existing tickets will retain their priority assignment.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const colorInput = document.getElementById('color');
    const preview = document.getElementById('priorityPreview');
    
    function updatePreview() {
        const name = nameInput.value || 'Priority Preview';
        const color = colorInput.value;
        
        preview.textContent = name;
        preview.style.backgroundColor = color;
        preview.style.color = 'white';
    }
    
    nameInput.addEventListener('input', updatePreview);
    colorInput.addEventListener('input', updatePreview);
});
</script>
