<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Create New Ticket</h2>
                    <p class="mb-md-0">Submit a new support request.</p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <a href="<?= baseUrl('tickets'); ?>" class="btn btn-secondary mb-2">
                    <i class="mdi mdi-arrow-left"></i> Back to Tickets
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('tickets'); ?>" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="subject">Subject *</label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               placeholder="Brief description of your issue" required>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="category_id">Category *</label>
                                <select name="category_id" id="category_id" class="form-control" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id']; ?>">
                                        <?= sanitize($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="priority_id">Priority *</label>
                                <select name="priority_id" id="priority_id" class="form-control" required>
                                    <option value="">Select Priority</option>
                                    <?php foreach ($priorities as $priority): ?>
                                    <option value="<?= $priority['id']; ?>">
                                        <?= sanitize($priority['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea class="form-control" id="description" name="description" rows="8" 
                                  placeholder="Detailed description of your issue. Please include any relevant information that will help us resolve your request." required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="attachments">Attachments</label>
                        <input type="file" class="form-control-file" id="attachments" name="attachments[]" multiple 
                               accept="image/*,video/*,.pdf,.doc,.docx,.txt,.zip,.rar">
                        <small class="form-text text-muted">
                            You can upload multiple files. Supported formats: Images, Videos, PDF, DOC, TXT, ZIP (Max 10MB per file)
                        </small>
                        <div id="file-preview" class="mt-2"></div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-send"></i> Create Ticket
                        </button>
                        <a href="<?= baseUrl('tickets'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Support Guidelines</h6>
                <div class="mt-3">
                    <h6>Before creating a ticket:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="mdi mdi-check-circle text-success"></i>
                            Check if your issue is already resolved
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-check-circle text-success"></i>
                            Provide detailed information
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-check-circle text-success"></i>
                            Choose the appropriate category
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-check-circle text-success"></i>
                            Set the correct priority level
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-check-circle text-success"></i>
                            Attach relevant files if needed
                        </li>
                    </ul>
                </div>
                
                <div class="mt-4">
                    <h6>Priority Levels:</h6>
                    <div class="mt-2">
                        <?php foreach ($priorities as $priority): ?>
                        <div class="mb-2">
                            <span class="badge" style="background-color: <?= $priority['color']; ?>; color: white;">
                                <?= sanitize($priority['name']); ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6>File Upload Guidelines:</h6>
                    <ul class="list-unstyled">
                        <li class="mb-1"><small>• Maximum file size: 10MB</small></li>
                        <li class="mb-1"><small>• Supported formats: Images, Videos, Documents</small></li>
                        <li class="mb-1"><small>• Multiple files allowed</small></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('attachments').addEventListener('change', function(e) {
    const preview = document.getElementById('file-preview');
    preview.innerHTML = '';
    
    if (e.target.files.length > 0) {
        const fileList = document.createElement('div');
        fileList.className = 'file-list mt-2';
        
        Array.from(e.target.files).forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item d-flex align-items-center mb-2 p-2 border rounded';
            
            const fileIcon = getFileIcon(file.type);
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            
            fileItem.innerHTML = `
                <i class="mdi ${fileIcon} mr-2"></i>
                <div class="flex-grow-1">
                    <div class="font-weight-bold">${file.name}</div>
                    <small class="text-muted">${fileSize} MB</small>
                </div>
            `;
            
            fileList.appendChild(fileItem);
        });
        
        preview.appendChild(fileList);
    }
});

function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return 'mdi-image';
    if (mimeType.startsWith('video/')) return 'mdi-video';
    if (mimeType.includes('pdf')) return 'mdi-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('document')) return 'mdi-file-word';
    if (mimeType.includes('zip') || mimeType.includes('rar')) return 'mdi-zip-box';
    return 'mdi-file';
}
</script>