<?php

class CategoryController extends Controller {
    
    /**
     * Show categories list (Admin only)
     */
    public function index() {
        $this->requireRole('admin');
        
        $categories = DB::table('categories')
            ->select(['id', 'name', 'description', 'status', 'created_at'])
            ->orderBy('name', 'ASC')
            ->get();
        
        $this->render('categories/index', [
            'title' => 'Category Management',
            'categories' => $categories
        ]);
    }
    
    /**
     * Show create category form (Admin only)
     */
    public function create() {
        $this->requireRole('admin');
        
        $this->render('categories/create', [
            'title' => 'Create New Category'
        ]);
    }
    
    /**
     * Handle create category request (Admin only)
     */
    public function store() {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $name = sanitize($this->getPost('name'));
            $description = sanitize($this->getPost('description'));
            $status = sanitize($this->getPost('status'));
            
            // Validate input
            if (empty($name)) {
                throw new Exception('Category name is required');
            }
            
            if (!in_array($status, ['active', 'inactive'])) {
                throw new Exception('Invalid status selected');
            }
            
            // Check if category name already exists
            $existingCategory = DB::table('categories')
                ->where('name', $name)
                ->first();
            
            if ($existingCategory) {
                throw new Exception('Category name already exists');
            }
            
            // Create category
            $categoryId = DB::table('categories')->insert([
                'name' => $name,
                'description' => $description,
                'status' => $status
            ]);
            
            // Clear old input on success
            clearOldInput();
            $this->setFlash('success', 'Category created successfully!');
            $this->redirect(baseUrl('categories'));
            
        } catch (Exception $e) {
            // Store old input for form repopulation
            setOldInput($_POST);
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('categories/create'));
        }
    }
    
    /**
     * Show edit category form (Admin only)
     */
    public function edit($id) {
        $this->requireRole('admin');
        
        $category = DB::table('categories')
            ->where('id', $id)
            ->first();
        
        if (!$category) {
            $this->setFlash('error', 'Category not found');
            $this->redirect(baseUrl('categories'));
        }
        
        $this->render('categories/edit', [
            'title' => 'Edit Category',
            'category' => $category
        ]);
    }
    
    /**
     * Handle update category request (Admin only)
     */
    public function update($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $name = sanitize($this->getPost('name'));
            $description = sanitize($this->getPost('description'));
            $status = sanitize($this->getPost('status'));
            
            // Validate input
            if (empty($name)) {
                throw new Exception('Category name is required');
            }
            
            if (!in_array($status, ['active', 'inactive'])) {
                throw new Exception('Invalid status selected');
            }
            
            // Check if category exists
            $category = DB::table('categories')
                ->where('id', $id)
                ->first();
            
            if (!$category) {
                throw new Exception('Category not found');
            }
            
            // Check if category name already exists (excluding current category)
            $existingCategory = DB::table('categories')
                ->where('name', $name)
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingCategory) {
                throw new Exception('Category name already exists');
            }
            
            // Update category
            DB::table('categories')
                ->where('id', $id)
                ->update([
                    'name' => $name,
                    'description' => $description,
                    'status' => $status
                ]);
            
            // Clear old input on success
            clearOldInput();
            $this->setFlash('success', 'Category updated successfully!');
            $this->redirect(baseUrl('categories'));
            
        } catch (Exception $e) {
            // Store old input for form repopulation
            setOldInput($_POST);
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('categories/edit/' . $id));
        }
    }
    
    /**
     * Handle delete category request (Admin only)
     */
    public function delete($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $category = DB::table('categories')
                ->where('id', $id)
                ->first();
            
            if (!$category) {
                throw new Exception('Category not found');
            }
            
            // Check if category is being used by any tickets
            $ticketCount = DB::table('tickets')
                ->where('category_id', $id)
                ->count();
            
            if ($ticketCount > 0) {
                throw new Exception('Cannot delete category. It is being used by ' . $ticketCount . ' ticket(s)');
            }
            
            // Delete category
            DB::delete('categories', ['id' => $id]);
            
            $this->setFlash('success', 'Category deleted successfully!');
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
        }
        
        $this->redirect(baseUrl('categories'));
    }
}
