<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Edit User</h2>
                    <p class="mb-md-0">Update user information and settings.</p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <a href="<?= baseUrl('users'); ?>" class="btn btn-secondary mb-2">
                    <i class="mdi mdi-arrow-left"></i> Back to Users
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('users/update/' . $user['id']); ?>">
                    <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= sanitize($user['name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= sanitize($user['email']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <small class="form-text text-muted">Leave empty to keep current password. Password must be at least 6 characters long.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="role">Role *</label>
                        <select name="role" id="role" class="form-control" required>
                            <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : ''; ?>>Administrator</option>
                            <option value="support" <?= $user['role'] === 'support' ? 'selected' : ''; ?>>Support Agent</option>
                            <option value="client" <?= $user['role'] === 'client' ? 'selected' : ''; ?>>Client</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="active" <?= $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?= $user['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-content-save"></i> Update User
                        </button>
                        <a href="<?= baseUrl('users'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">User Information</h6>
                <div class="mt-3">
                    <div class="info-item mb-3">
                        <strong>Current Role:</strong><br>
                        <span class="badge badge-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'support' ? 'warning' : 'info'); ?>">
                            <?= ucfirst($user['role']); ?>
                        </span>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Current Status:</strong><br>
                        <span class="badge badge-<?= $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                            <?= ucfirst($user['status']); ?>
                        </span>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Created:</strong><br>
                        <?= date('M d, Y H:i', strtotime($user['created_at'])); ?>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Last Updated:</strong><br>
                        <?= date('M d, Y H:i', strtotime($user['updated_at'])); ?>
                    </div>
                    
                    <?php if ($user['last_login']): ?>
                    <div class="info-item mb-3">
                        <strong>Last Login:</strong><br>
                        <?= date('M d, Y H:i', strtotime($user['last_login'])); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.info-item:last-child {
    border-bottom: none;
}
</style>
