<?php
/**
 * Create Category Page
 */
?>

<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
    <div>
        <h4 class="mb-3 mb-md-0">Create New Category</h4>
    </div>
    <div class="d-flex align-items-center flex-wrap text-nowrap">
        <a href="<?= baseUrl('categories'); ?>" class="btn btn-outline-primary btn-icon-text mb-2 mb-md-0">
            <i class="btn-icon-prepend" data-feather="arrow-left"></i>
            Back to Categories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Category Information</h6>
                
                <form method="POST" action="<?= baseUrl('categories'); ?>">
                    <?= csrf_field(); ?>
                    
                    <div class="form-group">
                        <label for="name">Category Name *</label>
                        <input type="text" name="name" id="name" class="form-control" 
                               value="<?= old('name'); ?>" required maxlength="100">
                        <small class="form-text text-muted">Enter a unique name for the category</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea name="description" id="description" class="form-control" rows="4" 
                                  placeholder="Optional description for the category"><?= old('description'); ?></textarea>
                        <small class="form-text text-muted">Provide a brief description of what this category is for</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status *</label>
                        <select name="status" id="status" class="form-control" required>
                            <option value="active" <?= old('status') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?= old('status') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                        <small class="form-text text-muted">Only active categories will be available for ticket creation</small>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i data-feather="save" class="icon-sm mr-2"></i>
                            Create Category
                        </button>
                        <a href="<?= baseUrl('categories'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Guidelines</h6>
                <div class="alert alert-info">
                    <h6 class="alert-heading">Category Best Practices</h6>
                    <ul class="mb-0">
                        <li>Use clear, descriptive names</li>
                        <li>Keep categories broad but meaningful</li>
                        <li>Avoid creating too many categories</li>
                        <li>Consider how clients will understand them</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">Note</h6>
                    <p class="mb-0">Category names must be unique. Once created, categories can be edited but should be used consistently.</p>
                </div>
            </div>
        </div>
    </div>
</div>
