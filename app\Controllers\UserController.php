<?php
/**
 * User Controller for authentication and user management
 */

require_once __DIR__ . '/../Core/Controller.php';

class UserController extends Controller {
    
    /**
     * Show login form
     */
    public function login() {
        // If already logged in, redirect to dashboard
        if (isLoggedIn()) {
            $this->redirect(baseUrl('dashboard'));
        }
        
        $this->render('auth/login', [
            'title' => 'Login'
        ]);
    }
    
    /**
     * Handle login request
     */
    public function doLogin() {
        try {
            $this->validateCSRF();
            
            $email = sanitize($this->getPost('email'));
            $password = $this->getPost('password');
            
            // Validate input
            if (empty($email) || empty($password)) {
                throw new Exception('Email and password are required');
            }
            
            if (!validateEmail($email)) {
                throw new Exception('Invalid email format');
            }
            
            // Find user
            $user = DB::table('users')
                ->where('email', $email)
                ->where('status', 'active')
                ->first();
            
            if (!$user || !verifyPassword($password, $user['password'])) {
                throw new Exception('Invalid credentials');
            }
            
            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_role'] = $user['role'];
            
            // Update last login
            DB::update('users', [
                'last_login' => date('Y-m-d H:i:s')
            ], ['id' => $user['id']]);
            
            $this->setFlash('success', 'Login successful!');
            $this->redirect(baseUrl('dashboard'));
            
        } catch (Exception $e) {
            $this->setFlash('danger', $e->getMessage());
            $this->redirect(baseUrl('login'));
        }
    }
    
    /**
     * Handle logout
     */
    public function logout() {
        // Clear session
        session_destroy();
        
        $this->setFlash('info', 'You have been logged out');
        $this->redirect(baseUrl('login'));
    }
    
    /**
     * Show user management page (Admin only)
     */
    public function index() {
        $this->requireRole('admin');
        
        $users = DB::table('users')
            ->select(['id', 'name', 'email', 'role', 'status', 'created_at', 'last_login'])
            ->orderBy('created_at', 'DESC')
            ->get();
        
        $this->render('users/index', [
            'title' => 'User Management',
            'users' => $users
        ]);
    }
    
    /**
     * Show create user form (Admin only)
     */
    public function create() {
        $this->requireRole('admin');
        
        $this->render('users/create', [
            'title' => 'Create New User'
        ]);
    }
    
    /**
     * Handle create user request (Admin only)
     */
    public function store() {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $name = sanitize($this->getPost('name'));
            $email = sanitize($this->getPost('email'));
            $password = $this->getPost('password');
            $role = sanitize($this->getPost('role'));
            
            // Validate input
            if (empty($name) || empty($email) || empty($password) || empty($role)) {
                throw new Exception('All fields are required');
            }
            
            if (!validateEmail($email)) {
                throw new Exception('Invalid email format');
            }
            
            if (strlen($password) < 6) {
                throw new Exception('Password must be at least 6 characters');
            }
            
            if (!in_array($role, ['admin', 'support', 'client'])) {
                throw new Exception('Invalid role selected');
            }
            
            // Check if email already exists
            $existingUser = DB::table('users')
                ->where('email', $email)
                ->first();
            
            if ($existingUser) {
                throw new Exception('Email already exists');
            }
            
            // Create user
            $userId = DB::table('users')->insert([
                'name' => $name,
                'email' => $email,
                'password' => hashPassword($password),
                'role' => $role,
                'status' => 'active'
            ]);
            
            $this->setFlash('success', 'User created successfully!');
            $this->redirect(baseUrl('users'));
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('users/create'));
        }
    }
    
    /**
     * Show edit user form (Admin only)
     */
    public function edit($id) {
        $this->requireRole('admin');
        
        $user = DB::table('users')
            ->where('id', $id)
            ->first();
        
        if (!$user) {
            $this->setFlash('error', 'User not found');
            $this->redirect(baseUrl('users'));
        }
        
        $this->render('users/edit', [
            'title' => 'Edit User',
            'user' => $user
        ]);
    }
    
    /**
     * Handle update user request (Admin only)
     */
    public function update($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $user = DB::table('users')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                throw new Exception('User not found');
            }
            
            $name = sanitize($this->getPost('name'));
            $email = sanitize($this->getPost('email'));
            $role = sanitize($this->getPost('role'));
            $status = sanitize($this->getPost('status'));
            $password = $this->getPost('password');
            
            // Validate input
            if (empty($name) || empty($email) || empty($role) || empty($status)) {
                throw new Exception('All fields are required');
            }
            
            if (!validateEmail($email)) {
                throw new Exception('Invalid email format');
            }
            
            if (!in_array($role, ['admin', 'support', 'client'])) {
                throw new Exception('Invalid role selected');
            }
            
            if (!in_array($status, ['active', 'inactive'])) {
                throw new Exception('Invalid status selected');
            }
            
            // Check if email already exists (excluding current user)
            $existingUser = DB::table('users')
                ->where('email', $email)
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingUser) {
                throw new Exception('Email already exists');
            }
            
            // Prepare update data
            $updateData = [
                'name' => $name,
                'email' => $email,
                'role' => $role,
                'status' => $status
            ];
            
            // Update password if provided
            if (!empty($password)) {
                if (strlen($password) < 6) {
                    throw new Exception('Password must be at least 6 characters');
                }
                $updateData['password'] = hashPassword($password);
            }
            
            // Update user
            DB::update('users', $updateData, ['id' => $id]);
            
            $this->setFlash('success', 'User updated successfully!');
            $this->redirect(baseUrl('users'));
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('users/edit/' . $id));
        }
    }
    
    /**
     * Handle delete user request (Admin only)
     */
    public function delete($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $user = DB::table('users')
                ->where('id', $id)
                ->first();
            
            if (!$user) {
                throw new Exception('User not found');
            }
            
            // Prevent deleting yourself
            if ($user['id'] == getCurrentUser()['id']) {
                throw new Exception('You cannot delete your own account');
            }
            
            // Delete user
            DB::delete('users', ['id' => $id]);
            
            $this->setFlash('success', 'User deleted successfully!');
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
        }
        
        $this->redirect(baseUrl('users'));
    }
    
    /**
     * Show user profile
     */
    public function profile() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        
        $this->render('users/profile', [
            'title' => 'My Profile',
            'user' => $user
        ]);
    }
    
    /**
     * Handle profile update
     */
    public function updateProfile() {
        $this->requireAuth();
        
        try {
            $this->validateCSRF();
            
            $user = getCurrentUser();
            $name = sanitize($this->getPost('name'));
            $email = sanitize($this->getPost('email'));
            $currentPassword = $this->getPost('current_password');
            $newPassword = $this->getPost('new_password');
            $confirmPassword = $this->getPost('confirm_password');
            
            // Validate input
            if (empty($name) || empty($email)) {
                throw new Exception('Name and email are required');
            }
            
            if (!validateEmail($email)) {
                throw new Exception('Invalid email format');
            }
            
            // Check if email already exists (excluding current user)
            $existingUser = DB::table('users')
                ->where('email', $email)
                ->where('id', '!=', $user['id'])
                ->first();
            
            if ($existingUser) {
                throw new Exception('Email already exists');
            }
            
            // Prepare update data
            $updateData = [
                'name' => $name,
                'email' => $email
            ];
            
            // Update password if provided
            if (!empty($newPassword)) {
                if (empty($currentPassword)) {
                    throw new Exception('Current password is required to change password');
                }
                
                // Get current user from database
                $dbUser = DB::table('users')
                    ->where('id', $user['id'])
                    ->first();
                
                if (!verifyPassword($currentPassword, $dbUser['password'])) {
                    throw new Exception('Current password is incorrect');
                }
                
                if (strlen($newPassword) < 6) {
                    throw new Exception('New password must be at least 6 characters');
                }
                
                if ($newPassword !== $confirmPassword) {
                    throw new Exception('New passwords do not match');
                }
                
                $updateData['password'] = hashPassword($newPassword);
            }
            
            // Update user
            DB::update('users', $updateData, ['id' => $user['id']]);
            
            // Update session
            $_SESSION['user_name'] = $name;
            $_SESSION['user_email'] = $email;
            
            $this->setFlash('success', 'Profile updated successfully!');
            $this->redirect(baseUrl('profile'));
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('profile'));
        }
    }
}
