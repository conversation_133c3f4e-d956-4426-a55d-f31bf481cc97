<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Create New User</h2>
                    <p class="mb-md-0">Add a new user to the system.</p>
                </div>
            </div>
            <div class="d-flex align-items-end flex-wrap">
                <a href="<?= baseUrl('users'); ?>" class="btn btn-secondary mb-2">
                    <i class="mdi mdi-arrow-left"></i> Back to Users
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('users'); ?>">
                    <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="role">Role *</label>
                        <select name="role" id="role" class="form-control" required>
                            <option value="">Select Role</option>
                            <option value="admin">Administrator</option>
                            <option value="support">Support Agent</option>
                            <option value="client">Client</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-account-plus"></i> Create User
                        </button>
                        <a href="<?= baseUrl('users'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">User Roles</h6>
                <div class="mt-3">
                    <div class="role-info mb-3">
                        <h6 class="text-danger">Administrator</h6>
                        <p class="text-muted">Full access to all system features including user management, ticket management, and system configuration.</p>
                    </div>
                    
                    <div class="role-info mb-3">
                        <h6 class="text-warning">Support Agent</h6>
                        <p class="text-muted">Can view and manage all tickets, reply to tickets, and update ticket statuses. Cannot manage users or system settings.</p>
                    </div>
                    
                    <div class="role-info mb-3">
                        <h6 class="text-info">Client</h6>
                        <p class="text-muted">Can create tickets, view their own tickets, and reply to their tickets. Cannot access admin or support features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
