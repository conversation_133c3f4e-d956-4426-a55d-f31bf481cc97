<?php

class PriorityController extends Controller {
    
    /**
     * Show priorities list (Admin only)
     */
    public function index() {
        $this->requireRole('admin');
        
        $priorities = DB::table('priorities')
            ->select(['id', 'name', 'level', 'color', 'status', 'created_at'])
            ->orderBy('level', 'ASC')
            ->get();
        
        $this->render('priorities/index', [
            'title' => 'Priority Management',
            'priorities' => $priorities
        ]);
    }
    
    /**
     * Show create priority form (Admin only)
     */
    public function create() {
        $this->requireRole('admin');
        
        $this->render('priorities/create', [
            'title' => 'Create New Priority'
        ]);
    }
    
    /**
     * Handle create priority request (Admin only)
     */
    public function store() {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $name = sanitize($this->getPost('name'));
            $level = (int) $this->getPost('level');
            $color = sanitize($this->getPost('color'));
            $status = sanitize($this->getPost('status'));
            
            // Validate input
            if (empty($name)) {
                throw new Exception('Priority name is required');
            }
            
            if ($level <= 0) {
                throw new Exception('Priority level must be a positive number');
            }
            
            if (empty($color) || !preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
                throw new Exception('Valid color code is required');
            }
            
            if (!in_array($status, ['active', 'inactive'])) {
                throw new Exception('Invalid status selected');
            }
            
            // Check if priority name already exists
            $existingPriority = DB::table('priorities')
                ->where('name', $name)
                ->first();
            
            if ($existingPriority) {
                throw new Exception('Priority name already exists');
            }
            
            // Check if priority level already exists
            $existingLevel = DB::table('priorities')
                ->where('level', $level)
                ->first();
            
            if ($existingLevel) {
                throw new Exception('Priority level already exists');
            }
            
            // Create priority
            $priorityId = DB::table('priorities')->insert([
                'name' => $name,
                'level' => $level,
                'color' => $color,
                'status' => $status
            ]);
            
            // Clear old input on success
            clearOldInput();
            $this->setFlash('success', 'Priority created successfully!');
            $this->redirect(baseUrl('priorities'));
            
        } catch (Exception $e) {
            // Store old input for form repopulation
            setOldInput($_POST);
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('priorities/create'));
        }
    }
    
    /**
     * Show edit priority form (Admin only)
     */
    public function edit($id) {
        $this->requireRole('admin');
        
        $priority = DB::table('priorities')
            ->where('id', $id)
            ->first();
        
        if (!$priority) {
            $this->setFlash('error', 'Priority not found');
            $this->redirect(baseUrl('priorities'));
        }
        
        $this->render('priorities/edit', [
            'title' => 'Edit Priority',
            'priority' => $priority
        ]);
    }
    
    /**
     * Handle update priority request (Admin only)
     */
    public function update($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $name = sanitize($this->getPost('name'));
            $level = (int) $this->getPost('level');
            $color = sanitize($this->getPost('color'));
            $status = sanitize($this->getPost('status'));
            
            // Validate input
            if (empty($name)) {
                throw new Exception('Priority name is required');
            }
            
            if ($level <= 0) {
                throw new Exception('Priority level must be a positive number');
            }
            
            if (empty($color) || !preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
                throw new Exception('Valid color code is required');
            }
            
            if (!in_array($status, ['active', 'inactive'])) {
                throw new Exception('Invalid status selected');
            }
            
            // Check if priority exists
            $priority = DB::table('priorities')
                ->where('id', $id)
                ->first();
            
            if (!$priority) {
                throw new Exception('Priority not found');
            }
            
            // Check if priority name already exists (excluding current priority)
            $existingPriority = DB::table('priorities')
                ->where('name', $name)
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingPriority) {
                throw new Exception('Priority name already exists');
            }
            
            // Check if priority level already exists (excluding current priority)
            $existingLevel = DB::table('priorities')
                ->where('level', $level)
                ->where('id', '!=', $id)
                ->first();
            
            if ($existingLevel) {
                throw new Exception('Priority level already exists');
            }
            
            // Update priority
            DB::table('priorities')
                ->where('id', $id)
                ->update([
                    'name' => $name,
                    'level' => $level,
                    'color' => $color,
                    'status' => $status
                ]);
            
            // Clear old input on success
            clearOldInput();
            $this->setFlash('success', 'Priority updated successfully!');
            $this->redirect(baseUrl('priorities'));
            
        } catch (Exception $e) {
            // Store old input for form repopulation
            setOldInput($_POST);
            $this->setFlash('error', $e->getMessage());
            $this->redirect(baseUrl('priorities/edit/' . $id));
        }
    }
    
    /**
     * Handle delete priority request (Admin only)
     */
    public function delete($id) {
        $this->requireRole('admin');
        
        try {
            $this->validateCSRF();
            
            $priority = DB::table('priorities')
                ->where('id', $id)
                ->first();
            
            if (!$priority) {
                throw new Exception('Priority not found');
            }
            
            // Check if priority is being used by any tickets
            $ticketCount = DB::table('tickets')
                ->where('priority_id', $id)
                ->count();
            
            if ($ticketCount > 0) {
                throw new Exception('Cannot delete priority. It is being used by ' . $ticketCount . ' ticket(s)');
            }
            
            // Delete priority
            DB::delete('priorities', ['id' => $id]);
            
            $this->setFlash('success', 'Priority deleted successfully!');
            
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
        }
        
        $this->redirect(baseUrl('priorities'));
    }
}
