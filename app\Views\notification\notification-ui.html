<!-- Add this to your main layout header -->
<style>
.notification-dropdown {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
    display: none;
}

.notification-dropdown-menu {
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-content h6 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
}

.notification-content p {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: #666;
}

.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast-notification {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
    transition: opacity 0.3s ease-out;
}

.toast-notification.toast-info {
    border-left: 4px solid #17a2b8;
}

.toast-notification.toast-success {
    border-left: 4px solid #28a745;
}

.toast-notification.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast-notification.toast-primary {
    border-left: 4px solid #007bff;
}

.toast-notification.fade-out {
    opacity: 0;
    transform: translateX(100%);
}

.toast-header {
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: between;
    align-items: center;
}

.toast-body {
    padding: 12px;
    font-size: 14px;
}

.toast-actions {
    margin-top: 8px;
}

.toast-actions .btn {
    font-size: 12px;
    padding: 4px 8px;
}

.close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

<!-- Notification Bell Icon (add to your header) -->
<div class="notification-dropdown dropdown">
    <button class="btn btn-link dropdown-toggle" type="button" id="notificationDropdown" 
            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="mdi mdi-bell-outline"></i>
        <span class="notification-badge">0</span>
    </button>
    
    <div class="dropdown-menu dropdown-menu-right notification-dropdown-menu" 
         aria-labelledby="notificationDropdown">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
            <span>Notifications</span>
            <button class="btn btn-sm btn-link" onclick="markAllNotificationsAsRead()">
                Mark all as read
            </button>
        </div>
        <div class="dropdown-divider"></div>
        
        <!-- Notifications will be loaded here -->
        <div class="text-center p-3">
            <i class="mdi mdi-bell-outline h3 text-muted"></i>
            <p class="text-muted">No notifications</p>
        </div>
    </div>
</div>

<script>
// Load notifications when dropdown is opened
document.getElementById('notificationDropdown').addEventListener('click', function() {
    loadNotifications();
});

function loadNotifications() {
    fetch('/notifications', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayNotifications(data.notifications);
        }
    })
    .catch(error => console.error('Failed to load notifications:', error));
}

function displayNotifications(notifications) {
    const menu = document.querySelector('.notification-dropdown-menu');
    const content = menu.querySelector('.dropdown-divider').nextElementSibling;
    
    if (notifications.length === 0) {
        content.innerHTML = `
            <div class="text-center p-3">
                <i class="mdi mdi-bell-outline h3 text-muted"></i>
                <p class="text-muted">No notifications</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    notifications.forEach(notification => {
        const data = JSON.parse(notification.data || '{}');
        html += `
            <div class="notification-item ${notification.is_read ? '' : 'unread'}" 
                 onclick="markNotificationAsRead(${notification.id})">
                <div class="notification-content">
                    <h6>${notification.title}</h6>
                    <p>${notification.message}</p>
                    <small class="text-muted">${formatDate(notification.created_at)}</small>
                </div>
            </div>
        `;
    });
    
    content.innerHTML = html;
}

function markNotificationAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            csrf_token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            const item = document.querySelector(`[onclick="markNotificationAsRead(${notificationId})"]`);
            if (item) {
                item.classList.remove('unread');
            }
            updateNotificationBadge();
        }
    })
    .catch(error => console.error('Failed to mark notification as read:', error));
}

function markAllNotificationsAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            csrf_token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update UI
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
            });
            updateNotificationBadge();
        }
    })
    .catch(error => console.error('Failed to mark all notifications as read:', error));
}

function updateNotificationBadge() {
    if (window.notificationClient) {
        window.notificationClient.updateNotificationBadge();
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return Math.floor(diff / 60000) + 'm ago';
    if (diff < 86400000) return Math.floor(diff / 3600000) + 'h ago';
    return Math.floor(diff / 86400000) + 'd ago';
}

// Load initial notification count
document.addEventListener('DOMContentLoaded', function() {
    updateNotificationBadge();
});
</script>