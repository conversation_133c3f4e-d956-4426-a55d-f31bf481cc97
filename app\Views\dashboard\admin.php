<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Welcome back, <?= sanitize($user['name']); ?>!</h2>
                    <p class="mb-md-0">Your admin dashboard overview.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Total Users</h6>
                    <div class="dropdown mb-2">
                        <button class="btn btn-link p-0" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="icon-more"></i>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a class="dropdown-item d-flex align-items-center" href="<?= baseUrl('users'); ?>">
                                <i data-feather="eye" class="icon-sm mr-2"></i>
                                <span class="">View</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['total_users']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-success">
                                <span>+3.3%</span>
                                <i data-feather="arrow-up" class="icon-sm mb-1"></i>
                            </p>
                        </div>
                    </div>
                    <div class="col-6 col-md-12 col-xl-7">
                        <div id="customersChart" class="mt-md-3 mt-xl-0"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Total Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['total_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-success">
                                <span>+2.8%</span>
                                <i data-feather="arrow-up" class="icon-sm mb-1"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Open Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['open_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-warning">
                                <span>-1.4%</span>
                                <i data-feather="arrow-down" class="icon-sm mb-1"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Closed Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['closed_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-success">
                                <span>+5.2%</span>
                                <i data-feather="arrow-up" class="icon-sm mb-1"></i>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-7 col-xl-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Recent Users</h6>
                    <div class="dropdown mb-2">
                        <button class="btn btn-link p-0" type="button" id="dropdownMenuButton2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="icon-more"></i>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                            <a class="dropdown-item d-flex align-items-center" href="<?= baseUrl('users'); ?>">
                                <i data-feather="eye" class="icon-sm mr-2"></i>
                                <span class="">View All</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="pt-0">Name</th>
                                <th class="pt-0">Email</th>
                                <th class="pt-0">Role</th>
                                <th class="pt-0">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentUsers as $user): ?>
                            <tr>
                                <td><?= sanitize($user['name']); ?></td>
                                <td><?= sanitize($user['email']); ?></td>
                                <td><span class="badge badge-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'support' ? 'warning' : 'info'); ?>"><?= ucfirst($user['role']); ?></span></td>
                                <td><?= date('M d, Y', strtotime($user['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-5 col-xl-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Quick Actions</h6>
                </div>
                <div class="d-flex flex-column">
                    <a href="<?= baseUrl('users/create'); ?>" class="btn btn-primary mb-2">
                        <i data-feather="plus" class="icon-sm mr-2"></i>
                        Create New User
                    </a>
                    <a href="<?= baseUrl('users'); ?>" class="btn btn-outline-primary mb-2">
                        <i data-feather="users" class="icon-sm mr-2"></i>
                        Manage Users
                    </a>
                    <a href="<?= baseUrl('categories'); ?>" class="btn btn-outline-info mb-2">
                        <i data-feather="tag" class="icon-sm mr-2"></i>
                        Manage Categories
                    </a>
                    <a href="<?= baseUrl('priorities'); ?>" class="btn btn-outline-warning mb-2">
                        <i data-feather="flag" class="icon-sm mr-2"></i>
                        Manage Priorities
                    </a>
                    <a href="<?= baseUrl('tickets'); ?>" class="btn btn-outline-secondary">
                        <i data-feather="file-text" class="icon-sm mr-2"></i>
                        View All Tickets
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
