<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>My Profile</h2>
                    <p class="mb-md-0">Update your personal information and settings.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <form method="POST" action="<?= baseUrl('profile'); ?>">
                    <input type="hidden" name="csrf_token" value="<?= $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="name">Full Name *</label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= sanitize($user['name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= sanitize($user['email']); ?>" required>
                    </div>
                    
                    <hr>
                    
                    <h6>Change Password</h6>
                    <p class="text-muted">Leave password fields empty if you don't want to change your password.</p>
                    
                    <div class="form-group">
                        <label for="current_password">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password">
                    </div>
                    
                    <div class="form-group">
                        <label for="new_password">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password">
                        <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="mdi mdi-content-save"></i> Update Profile
                        </button>
                        <a href="<?= baseUrl('dashboard'); ?>" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Account Information</h6>
                <div class="mt-3">
                    <div class="info-item mb-3">
                        <strong>Role:</strong><br>
                        <span class="badge badge-<?= $user['role'] === 'admin' ? 'danger' : ($user['role'] === 'support' ? 'warning' : 'info'); ?>">
                            <?= ucfirst($user['role']); ?>
                        </span>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>User ID:</strong><br>
                        #<?= $user['id']; ?>
                    </div>
                    
                    <div class="info-item mb-3">
                        <strong>Member Since:</strong><br>
                        <?= date('M d, Y', strtotime($user['created_at'])); ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Security Tips</h6>
                <div class="mt-3">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="mdi mdi-shield-check text-success"></i>
                            Use a strong, unique password
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-shield-check text-success"></i>
                            Keep your contact information updated
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-shield-check text-success"></i>
                            Don't share your account credentials
                        </li>
                        <li class="mb-2">
                            <i class="mdi mdi-shield-check text-success"></i>
                            Log out when using shared computers
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.info-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.info-item:last-child {
    border-bottom: none;
}
</style>
