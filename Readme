# Ticketing System

A web-based ticketing system built with PHP for managing support tickets, users, and administrative tasks.

## Features

- User authentication and authorization
- Role-based access control (Admin, Support, Client)
- Ticket creation, management, and tracking
- User management
- Dashboard for different user roles
- File upload functionality
- Responsive design

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server
- XAMPP (recommended for local development)

## Installation

### 1. Clone/Download the Project

Place the project files in your XAMPP htdocs directory:
```
C:\xampp\htdocs\ticketing\
```

### 2. Database Setup

1. Start XAMPP and ensure MySQL is running
2. Open phpMyAdmin (http://localhost/phpmyadmin)
3. Create a new database called `ticketing_db`
4. Import the database schema from `sql/schema.sql`

### 3. Configuration

1. Copy the configuration file and update database settings:
   - Edit `app/Config/config.php`
   - Update database connection parameters

### 4. XAMPP Virtual Host Setup

To set up a virtual host in XAMPP for better development experience:

#### Step 1: Edit Apache Configuration

1. Open XAMPP Control Panel
2. Click "Config" next to Apache
3. Select "Apache (httpd.conf)"
4. Find the line `#Include conf/extra/httpd-vhosts.conf`
5. Remove the `#` to uncomment it:
   ```apache
   Include conf/extra/httpd-vhosts.conf
   ```

#### Step 2: Configure Virtual Host

1. From XAMPP Control Panel, click "Config" next to Apache
2. Select "Apache (C:\xampp\apache\conf\extra\httpd-vhosts.conf)"
3. Add the following virtual host configuration at the end of the file:

```apache
<VirtualHost *:80>
    DocumentRoot "C:/xampp/htdocs/ticketing"
    ServerName ticketing.local
    <Directory "C:/xampp/htdocs/ticketing">
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>

```

#### Step 3: Update Windows Hosts File

1. Open Command Prompt as Administrator
2. Navigate to `C:\Windows\System32\drivers\etc\`
3. Edit the `hosts` file (you can use notepad):
   ```
   notepad hosts
   ```
4. Add the following line at the end:
   ```
   127.0.0.1    ticketing.local
   127.0.0.1    www.ticketing.local
   ```

#### Step 4: Restart Apache

1. In XAMPP Control Panel, stop Apache
2. Start Apache again
3. You should now be able to access your ticketing system at: `http://ticketing.local`

## Project Structure

```
ticketing/
├── app/
│   ├── Config/         # Configuration files
│   ├── Controllers/    # Application controllers
│   ├── Core/          # Core framework files
│   ├── Helpers/       # Helper functions
│   └── Views/         # View templates
├── assets/
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   ├── images/        # Images and icons
│   └── vendors/       # Third-party libraries
├── sql/               # Database schema
├── uploads/           # File uploads directory
├── docker-compose.yml # Docker configuration
├── Dockerfile         # Docker image configuration
└── index.php          # Application entry point
```

## Usage

### Default Access

After setting up the virtual host:
- Visit: `http://ticketing.local`
- Default admin credentials (if seeded): 
  - Username: admin
  - Password: admin123

### User Roles

1. **Admin**: Full system access, user management, ticket oversight
2. **Support**: Handle tickets, view user information
3. **Client**: Create tickets, view own tickets

## Development

### Local Development with XAMPP

1. Ensure XAMPP is running (Apache + MySQL)
2. Access the application via the virtual host: `http://ticketing.local`
3. Check error logs in XAMPP logs directory if issues occur

### Docker Alternative

If you prefer using Docker:

```bash
# Build and run with docker-compose
docker-compose up -d

# Access at http://localhost:8080
```

## File Permissions

Ensure the following directories have write permissions:
- `uploads/` - For file uploads
- `logs/` - For application logs (if logging is enabled)

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `app/Config/config.php`
   - Ensure MySQL is running in XAMPP

2. **Virtual Host Not Working**
   - Verify Apache configuration is correct
   - Check Windows hosts file entries
   - Restart Apache after making changes

3. **File Upload Issues**
   - Check `uploads/` directory permissions
   - Verify PHP upload settings in `php.ini`

4. **CSS/JS Not Loading**
   - Check file paths in templates
   - Verify Apache can serve static files

### Error Logs

Check the following logs for debugging:
- `error.log` - Application errors
- XAMPP Apache error logs
- Virtual host specific logs (if configured)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please create a ticket in the system or contact the development team.