/**
 * WebSocket Notification Client
 * Include this in your main layout
 */

class NotificationClient {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.isAuthenticated = false;
        this.userId = null;
        this.token = null;
        
        this.init();
    }
    
    async init() {
        try {
            // Get WebSocket token from server
            const response = await fetch('/notifications/websocket-token', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const data = await response.json();
            if (data.success) {
                this.userId = data.user_id;
                this.token = data.token;
                this.connect();
            }
        } catch (error) {
            console.error('Failed to get WebSocket token:', error);
        }
    }
    
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            return;
        }
        
        try {
            this.ws = new WebSocket('ws://localhost:8080');
            
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.reconnectAttempts = 0;
                this.authenticate();
                this.startHeartbeat();
            };
            
            this.ws.onmessage = (event) => {
                this.handleMessage(JSON.parse(event.data));
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.isAuthenticated = false;
                this.stopHeartbeat();
                this.reconnect();
            };
            
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
            
        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            this.reconnect();
        }
    }
    
    authenticate() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN && this.userId && this.token) {
            this.ws.send(JSON.stringify({
                type: 'auth',
                user_id: this.userId,
                token: this.token
            }));
        }
    }
    
    handleMessage(data) {
        switch (data.type) {
            case 'auth_success':
                this.isAuthenticated = true;
                console.log('WebSocket authenticated');
                break;
                
            case 'auth_error':
                console.error('WebSocket authentication failed:', data.message);
                break;
                
            case 'notification':
                this.handleNotification(data.data);
                break;
                
            case 'pong':
                // Heartbeat response
                break;
        }
    }
    
    handleNotification(notification) {
        console.log('New notification:', notification);

        // Update notification badge
        this.updateNotificationBadge();

        // Show toast notification
        this.showToast(notification);

        // Add to notification dropdown
        this.addToNotificationDropdown(notification);

        // Play notification sound (optional)
        this.playNotificationSound();

        // Handle specific notification types
        this.handleSpecificNotificationType(notification);

        // Trigger custom event for other parts of the application
        this.dispatchNotificationEvent(notification);
    }

    handleSpecificNotificationType(notification) {
        switch (notification.type) {
            case 'new_ticket':
                this.handleNewTicketNotification(notification);
                break;
            case 'ticket_status_change':
                this.handleTicketStatusChangeNotification(notification);
                break;
            case 'ticket_reply':
                this.handleTicketReplyNotification(notification);
                break;
            case 'ticket_assignment':
                this.handleTicketAssignmentNotification(notification);
                break;
        }
    }

    handleNewTicketNotification(notification) {
        // If we're on the tickets page, we might want to refresh the list
        if (window.location.pathname.includes('/tickets') && !window.location.pathname.includes('/tickets/')) {
            // Optionally refresh the ticket list or add the new ticket dynamically
            console.log('New ticket notification on tickets page');
        }
    }

    handleTicketStatusChangeNotification(notification) {
        // If we're viewing the specific ticket, update the status display
        const ticketId = notification.data?.ticket_id;
        if (ticketId && window.location.pathname.includes(`/tickets/${ticketId}`)) {
            // Optionally refresh the page or update the status dynamically
            console.log('Ticket status changed for current ticket');
        }
    }

    handleTicketReplyNotification(notification) {
        // If we're viewing the specific ticket, we might want to load the new reply
        const ticketId = notification.data?.ticket_id;
        if (ticketId && window.location.pathname.includes(`/tickets/${ticketId}`)) {
            console.log('New reply on current ticket');
        }
    }

    handleTicketAssignmentNotification(notification) {
        // Handle ticket assignment notifications
        console.log('Ticket assignment notification');
    }

    dispatchNotificationEvent(notification) {
        // Dispatch a custom event that other parts of the application can listen to
        const event = new CustomEvent('newNotification', {
            detail: notification
        });
        document.dispatchEvent(event);
    }
    
    updateNotificationBadge() {
        fetch('/notifications/unread-count', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }
        })
        .catch(error => console.error('Failed to update notification badge:', error));
    }
    
    showToast(notification) {
        // Determine toast type based on notification type
        let toastClass = 'toast-notification';
        let iconClass = 'mdi-bell';

        switch (notification.type) {
            case 'new_ticket':
                toastClass += ' toast-info';
                iconClass = 'mdi-ticket';
                break;
            case 'ticket_status_change':
                toastClass += ' toast-warning';
                iconClass = 'mdi-update';
                break;
            case 'ticket_reply':
                toastClass += ' toast-success';
                iconClass = 'mdi-reply';
                break;
            case 'ticket_assignment':
                toastClass += ' toast-primary';
                iconClass = 'mdi-account-check';
                break;
            default:
                toastClass += ' toast-default';
        }

        // Create toast notification
        const toast = document.createElement('div');
        toast.className = toastClass;
        toast.innerHTML = `
            <div class="toast-header">
                <i class="mdi ${iconClass} mr-2"></i>
                <strong>${notification.title}</strong>
                <button type="button" class="close" onclick="this.parentElement.parentElement.remove()">
                    <span>&times;</span>
                </button>
            </div>
            <div class="toast-body">
                ${notification.message}
                ${this.getNotificationActions(notification)}
            </div>
        `;

        // Add to page
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Auto remove after 7 seconds (increased for better UX)
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.add('fade-out');
                setTimeout(() => toast.remove(), 300);
            }
        }, 7000);
    }

    getNotificationActions(notification) {
        const data = notification.data || {};
        let actions = '';

        if (notification.type === 'new_ticket' && data.ticket_id) {
            actions = `<div class="toast-actions mt-2">
                <a href="/tickets/${data.ticket_id}" class="btn btn-sm btn-primary">View Ticket</a>
            </div>`;
        } else if (notification.type === 'ticket_reply' && data.ticket_id) {
            actions = `<div class="toast-actions mt-2">
                <a href="/tickets/${data.ticket_id}" class="btn btn-sm btn-success">View Reply</a>
            </div>`;
        } else if (notification.type === 'ticket_assignment' && data.ticket_id) {
            actions = `<div class="toast-actions mt-2">
                <a href="/tickets/${data.ticket_id}" class="btn btn-sm btn-primary">View Assigned Ticket</a>
            </div>`;
        }

        return actions;
    }
    
    addToNotificationDropdown(notification) {
        const dropdown = document.querySelector('.notification-dropdown-menu');
        if (dropdown) {
            const item = document.createElement('div');
            item.className = 'notification-item unread';
            item.innerHTML = `
                <div class="notification-content">
                    <h6>${notification.title}</h6>
                    <p>${notification.message}</p>
                    <small class="text-muted">Just now</small>
                </div>
            `;
            
            dropdown.insertBefore(item, dropdown.firstChild);
        }
    }
    
    playNotificationSound() {
        // Optional: Play notification sound
        const audio = new Audio('/assets/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(e => {
            // Ignore audio play errors (user interaction required)
        });
    }
    
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({ type: 'ping' }));
            }
        }, 30000); // 30 seconds
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    reconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
            setTimeout(() => {
                this.connect();
            }, this.reconnectDelay * this.reconnectAttempts);
        } else {
            console.error('Max reconnection attempts reached');
        }
    }
    
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }
}

// Initialize notification client when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.notificationClient = new NotificationClient();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.notificationClient) {
        window.notificationClient.disconnect();
    }
});