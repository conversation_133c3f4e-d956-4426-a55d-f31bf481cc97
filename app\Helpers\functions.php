<?php
/**
 * Helper functions for the ticketing application
 */

// Ensure session is started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Redirect to a URL
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * Get the base URL
 */
function baseUrl($path = '') {
    global $config;
    
    if (!empty($config['base_url'])) {
        $baseUrl = $config['base_url'];
    } else {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $scriptDir = dirname($_SERVER['SCRIPT_NAME']);
        
        // Normalize script directory
        if ($scriptDir === '/' || $scriptDir === '\\') {
            $scriptDir = '';
        }
        
        $baseUrl = $protocol . '://' . $host . $scriptDir;
    }
    
    // Clean up the base URL and path
    $baseUrl = rtrim($baseUrl, '/');
    $path = ltrim($path, '/');
    
    // Combine base URL and path
    $fullUrl = $baseUrl . '/' . $path;
    
    // Remove any double slashes except after protocol
    $fullUrl = preg_replace('/([^:])\/+/', '$1/', $fullUrl);
    
    // Debug logging
    if (isset($config['debug_mode']) && $config['debug_mode']) {
        error_log("baseUrl Debug - Input path: " . $path);
        error_log("baseUrl Debug - Base URL: " . $baseUrl);
        error_log("baseUrl Debug - Full URL: " . $fullUrl);
    }
    
    return $fullUrl;
}

/**
 * Get the base URL (alias for baseUrl)
 */
function base_url($path = '') {
    return baseUrl($path);
}

/**
 * Get asset URL
 */
function asset_url($path = '') {
    return base_url('assets/' . ltrim($path, '/'));
}

/**
 * Get current URL
 */
function currentUrl() {
    return $_SERVER['REQUEST_URI'];
}

/**
 * Check if user is logged in (alias for isLoggedIn)
 */
function is_authenticated() {
    return isLoggedIn();
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Get current user
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'email' => $_SESSION['user_email'],
        'name' => $_SESSION['user_name'],
        'role' => $_SESSION['user_role']
    ];
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    $user = getCurrentUser();
    return $user && $user['role'] === $role;
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return hasRole('admin');
}

/**
 * Check if user is support/agent
 */
function isSupport() {
    return hasRole('support');
}

/**
 * Check if user is client
 */
function isClient() {
    return hasRole('client');
}

/**
 * Require authentication
 */
function requireAuth() {
    if (!isLoggedIn()) {
        redirect(baseUrl('login'));
    }
}

/**
 * Require specific role
 */
function requireRole($role) {
    requireAuth();
    if (!hasRole($role)) {
        redirect(baseUrl('unauthorized'));
    }
}

/**
 * Sanitize input
 */
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Hash password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Set flash message
 */
function setFlash($type, $message) {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // Initialize flash array if it doesn't exist
    if (!isset($_SESSION['flash'])) {
        $_SESSION['flash'] = [];
    }
    
    // Validate flash type
    $validTypes = ['success', 'danger', 'info', 'warning', 'primary', 'secondary', 'light', 'dark'];
    if (!in_array($type, $validTypes)) {
        $type = 'info'; // Default to info if invalid type
    }
    
    $_SESSION['flash'][$type] = $message;
}

/**
 * Get flash message
 */
function getFlash($type) {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

/**
 * Get all flash messages
 */
function get_flashes() {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['flash'])) {
        return [];
    }

    $flashes = [];
    foreach ($_SESSION['flash'] as $type => $message) {
        $flashes[] = ['type' => $type, 'message' => $message];
    }

    // Clear flashes after getting them
    unset($_SESSION['flash']);

    return $flashes;
}

/**
 * Get old input value (for form repopulation after validation errors)
 */
function old($key, $default = '') {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    if (isset($_SESSION['old_input'][$key])) {
        $value = $_SESSION['old_input'][$key];
        // Don't unset here - let it persist until next request
        return $value;
    }

    return $default;
}

/**
 * Set old input values (called when validation fails)
 */
function setOldInput($data) {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    $_SESSION['old_input'] = $data;
}

/**
 * Clear old input values (called on successful form submission)
 */
function clearOldInput() {
    // Ensure session is started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    unset($_SESSION['old_input']);
}

/**
 * Set success flash message
 */
function setSuccess($message) {
    setFlash('success', $message);
}

/**
 * Set error flash message
 */
function setError($message) {
    setFlash('danger', $message);
}

/**
 * Set info flash message  
 */
function setInfo($message) {
    setFlash('info', $message);
}

/**
 * Set warning flash message
 */
function setWarning($message) {
    setFlash('warning', $message);
}

/**
 * Log error
 */
function logError($error) {
    $message = date('Y-m-d H:i:s') . ' - ' . $error . PHP_EOL;
    error_log($message, 3, 'error.log');
}

/**
 * Debug function
 */
function debug($data) {
    global $config;
    if ($config['debug_mode']) {
        echo '<pre>';
        print_r($data);
        echo '</pre>';
    }
}

/**
 * Include view
 */
function view($viewName, $data = []) {
    extract($data);
    $viewPath = __DIR__ . '/../Views/' . $viewName . '.php';
    if (file_exists($viewPath)) {
        include $viewPath;
    } else {
        throw new Exception("View not found: $viewName");
    }
}

/**
 * Include layout
 */
function layout($layoutName, $content, $data = []) {
    extract($data);
    $layoutPath = __DIR__ . '/../Views/layouts/' . $layoutName . '.php';
    if (file_exists($layoutPath)) {
        include $layoutPath;
    } else {
        throw new Exception("Layout not found: $layoutName");
    }
}
