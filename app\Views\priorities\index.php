<?php
/**
 * Priorities Management Page
 */
?>

<div class="d-flex justify-content-between align-items-center flex-wrap grid-margin">
    <div>
        <h4 class="mb-3 mb-md-0">Priority Management</h4>
    </div>
    <div class="d-flex align-items-center flex-wrap text-nowrap">
        <a href="<?= baseUrl('priorities/create'); ?>" class="btn btn-primary btn-icon-text mb-2 mb-md-0">
            <i class="btn-icon-prepend" data-feather="plus"></i>
            Add Priority
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-12 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Priorities</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Level</th>
                                <th>Name</th>
                                <th>Color</th>
                                <th>Preview</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($priorities)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No priorities found</td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($priorities as $priority): ?>
                                <tr>
                                    <td>
                                        <span class="badge badge-light"><?= $priority['level']; ?></span>
                                    </td>
                                    <td>
                                        <strong><?= sanitize($priority['name']); ?></strong>
                                    </td>
                                    <td>
                                        <code><?= sanitize($priority['color']); ?></code>
                                    </td>
                                    <td>
                                        <span class="badge" style="background-color: <?= $priority['color']; ?>; color: white;">
                                            <?= sanitize($priority['name']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($priority['status'] === 'active'): ?>
                                            <span class="badge badge-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= date('M j, Y', strtotime($priority['created_at'])); ?>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-toggle="dropdown">
                                                Actions
                                            </button>
                                            <div class="dropdown-menu">
                                                <a class="dropdown-item" href="<?= baseUrl('priorities/edit/' . $priority['id']); ?>">
                                                    <i data-feather="edit-2" class="icon-sm mr-2"></i>
                                                    Edit
                                                </a>
                                                <div class="dropdown-divider"></div>
                                                <a class="dropdown-item text-danger" href="#" onclick="confirmDelete(<?= $priority['id']; ?>, '<?= sanitize($priority['name']); ?>')">
                                                    <i data-feather="trash-2" class="icon-sm mr-2"></i>
                                                    Delete
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the priority "<span id="priorityName"></span>"?</p>
                <p class="text-warning"><small>This action cannot be undone. Priorities that are being used by tickets cannot be deleted.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field(); ?>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(priorityId, priorityName) {
    document.getElementById('priorityName').textContent = priorityName;
    document.getElementById('deleteForm').action = '<?= baseUrl('priorities/delete/'); ?>' + priorityId;
    $('#deleteModal').modal('show');
}
</script>
