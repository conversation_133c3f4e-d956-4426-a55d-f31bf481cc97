<?php
/**
 * Ticket Controller for managing tickets
 */

require_once __DIR__ . '/../Core/Controller.php';
require_once __DIR__ . '/../Notifications/NotificationManager.php';

class TicketController extends Controller {
    
    /**
     * Show tickets list
     */
    public function index() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        $query = DB::table('tickets as t')
            ->join('users as u', 't.client_id', '=', 'u.id')
            ->join('categories as c', 't.category_id', '=', 'c.id')
            ->join('priorities as p', 't.priority_id', '=', 'p.id')
            ->leftJoin('users as a', 't.assigned_to', '=', 'a.id')
            ->select([
                't.id', 't.ticket_number', 't.subject', 't.status', 't.created_at',
                'u.name as client_name', 'c.name as category_name', 
                'p.name as priority_name', 'p.color as priority_color',
                'a.name as assigned_name'
            ]);
        
        // Filter based on user role
        if ($user['role'] === 'client') {
            $query->where('t.client_id', $user['id']);
        } elseif ($user['role'] === 'support') {
            // Support agents can see all tickets or just assigned ones
            $filter = $this->getGet('filter', 'all');
            if ($filter === 'assigned') {
                $query->where('t.assigned_to', $user['id']);
            }
        }
        
        // Status filter
        $statusFilter = $this->getGet('status');
        if ($statusFilter) {
            $query->where('t.status', $statusFilter);
        }
        
        // Priority filter
        $priorityFilter = $this->getGet('priority');
        if ($priorityFilter) {
            $query->where('t.priority_id', $priorityFilter);
        }
        
        $tickets = $query->orderBy('t.created_at', 'DESC')->get();
        
        // Get filter options
        $categories = DB::table('categories')->where('status', 'active')->get();
        $priorities = DB::table('priorities')->where('status', 'active')->get();
        
        $this->render('tickets/index', [
            'title' => 'Tickets',
            'tickets' => $tickets,
            'categories' => $categories,
            'priorities' => $priorities,
            'currentFilters' => [
                'status' => $statusFilter,
                'priority' => $priorityFilter
            ]
        ]);
    }
    
    /**
     * Show ticket details
     */
    public function show($id) {
        $this->requireAuth();
        
        $user = getCurrentUser();
        
        // Get ticket with relations
        $ticket = DB::table('tickets as t')
            ->join('users as u', 't.client_id', '=', 'u.id')
            ->join('categories as c', 't.category_id', '=', 'c.id')
            ->join('priorities as p', 't.priority_id', '=', 'p.id')
            ->leftJoin('users as a', 't.assigned_to', '=', 'a.id')
            ->select([
                't.*', 'u.name as client_name', 'u.email as client_email',
                'c.name as category_name', 'p.name as priority_name', 
                'p.color as priority_color', 'a.name as assigned_name'
            ])
            ->where('t.id', $id)
            ->first();
        
        if (!$ticket) {
            $this->setFlash('error', 'Ticket not found');
            $this->redirect(baseUrl('tickets'));
        }
        
        // Check permissions
        if ($user['role'] === 'client' && $ticket['client_id'] != $user['id']) {
            $this->setFlash('error', 'You do not have permission to view this ticket');
            $this->redirect(baseUrl('tickets'));
        }
        
        // Get ticket replies
        $replies = DB::table('ticket_replies as tr')
            ->join('users as u', 'tr.user_id', '=', 'u.id')
            ->select(['tr.*', 'u.name as user_name', 'u.role as user_role'])
            ->where('tr.ticket_id', $id)
            ->orderBy('tr.created_at', 'ASC')
            ->get();
        
        // Filter internal notes for clients
        if ($user['role'] === 'client') {
            $replies = array_filter($replies, function($reply) {
                return !$reply['is_internal'];
            });
        }
        
        // Get ticket attachments (only those not associated with replies)
        $attachments = DB::table('ticket_attachments as ta')
            ->join('users as u', 'ta.uploaded_by', '=', 'u.id')
            ->select(['ta.*', 'u.name as uploaded_by_name'])
            ->where('ta.ticket_id', $id)
            ->whereNull('ta.reply_id')
            ->orderBy('ta.created_at', 'ASC')
            ->get();

        // Get reply attachments for each reply
        $replyAttachments = [];
        if (!empty($replies)) {
            $replyIds = array_column($replies, 'id');
            $allReplyAttachments = DB::table('ticket_attachments as ta')
                ->join('users as u', 'ta.uploaded_by', '=', 'u.id')
                ->select(['ta.*', 'u.name as uploaded_by_name'])
                ->where('ta.ticket_id', $id)
                ->whereNotNull('ta.reply_id')
                ->whereIn('ta.reply_id', $replyIds)
                ->orderBy('ta.created_at', 'ASC')
                ->get();

            // Group attachments by reply_id
            foreach ($allReplyAttachments as $attachment) {
                $replyAttachments[$attachment['reply_id']][] = $attachment;
            }
        }
        
        $this->render('tickets/show', [
            'title' => 'Ticket #' . $ticket['ticket_number'],
            'ticket' => $ticket,
            'replies' => $replies,
            'attachments' => $attachments,
            'replyAttachments' => $replyAttachments
        ]);
    }
    
    /**
     * Show create ticket form
     */
    public function create() {
        $this->requireAuth();
        
        // Only clients can create tickets
        if (getCurrentUser()['role'] !== 'client') {
            $this->setFlash('error', 'Only clients can create tickets');
            $this->redirect(baseUrl('tickets'));
        }
        
        $categories = DB::table('categories')->where('status', 'active')->get();
        $priorities = DB::table('priorities')->where('status', 'active')->get();
        
        $this->render('tickets/create', [
            'title' => 'Create New Ticket',
            'categories' => $categories,
            'priorities' => $priorities
        ]);
    }
    
    /**
     * Handle create ticket request
     */
    public function store() {
    $this->requireAuth();
    
    try {
        $this->validateCSRF();
        
        $user = getCurrentUser();
        
        // Only clients can create tickets
        if ($user['role'] !== 'client') {
            throw new Exception('Only clients can create tickets');
        }
        
        $subject = sanitize($this->getPost('subject'));
        $description = sanitize($this->getPost('description'));
        $categoryId = (int) $this->getPost('category_id');
        $priorityId = (int) $this->getPost('priority_id');
        
        // Validate input
        if (empty($subject) || empty($description)) {
            throw new Exception('Subject and description are required');
        }
        
        if ($categoryId <= 0 || $priorityId <= 0) {
            throw new Exception('Please select a category and priority');
        }
        
        // Generate ticket number
        $ticketNumber = $this->generateTicketNumber();
        
        // Create ticket
        $ticketId = DB::table('tickets')->insert([
            'ticket_number' => $ticketNumber,
            'subject' => $subject,
            'description' => $description,
            'category_id' => $categoryId,
            'priority_id' => $priorityId,
            'client_id' => $user['id'],
            'status' => 'open',
        ]);
        
        // Handle file uploads
        if (!empty($_FILES['attachments']['name'][0])) {
            $this->handleFileUploads($ticketId, $user['id']);
        }
        
        // Send notification to admins and support
        $notificationManager = NotificationManager::getInstance();
        $notificationManager->notifyNewTicket($ticketId, $user['id']);
        
        $this->setFlash('success', 'Ticket created successfully! Ticket #' . $ticketNumber);
        $this->redirect(baseUrl('tickets/' . $ticketId));
        
    } catch (Exception $e) {
        $this->setFlash('error', $e->getMessage());
        $this->redirect(baseUrl('tickets/create'));
    }
}
    
    /**
     * Handle file uploads for tickets
     */
    private function handleFileUploads($ticketId, $userId, $replyId = null) {
        $uploadDir = __DIR__ . '/../../uploads/tickets/';
        
        // Create upload directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
            'application/pdf', 'application/msword', 
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain', 'application/zip', 'application/x-rar-compressed'
        ];
        
        $maxFileSize = 10 * 1024 * 1024; // 10MB
        
        foreach ($_FILES['attachments']['tmp_name'] as $key => $tmpName) {
            if (empty($tmpName)) continue;
            
            $originalName = $_FILES['attachments']['name'][$key];
            $fileSize = $_FILES['attachments']['size'][$key];
            $mimeType = $_FILES['attachments']['type'][$key];
            $error = $_FILES['attachments']['error'][$key];
            
            // Check for upload errors
            if ($error !== UPLOAD_ERR_OK) {
                throw new Exception("Upload error for file: $originalName");
            }
            
            // Check file size
            if ($fileSize > $maxFileSize) {
                throw new Exception("File too large: $originalName (Max 10MB)");
            }
            
            // Check file type
            if (!in_array($mimeType, $allowedTypes)) {
                throw new Exception("File type not allowed: $originalName");
            }
            
            // Generate unique filename
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $filename = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($tmpName, $filePath)) {
                throw new Exception("Failed to upload file: $originalName");
            }
            
            // Save to database
            DB::table('ticket_attachments')->insert([
                'ticket_id' => $ticketId,
                'reply_id' => $replyId,
                'filename' => $filename,
                'original_filename' => $originalName,
                'file_size' => $fileSize,
                'mime_type' => $mimeType,
                'uploaded_by' => $userId
            ]);
        }
    }
    
    /**
     * Download attachment
     */
    public function downloadAttachment($id) {
        $this->requireAuth();
        
        $user = getCurrentUser();
        
        // Get attachment with ticket info
        $attachment = DB::table('ticket_attachments as ta')
            ->join('tickets as t', 'ta.ticket_id', '=', 't.id')
            ->select(['ta.*', 't.client_id'])
            ->where('ta.id', $id)
            ->first();
        
        if (!$attachment) {
            $this->setFlash('error', 'Attachment not found');
            $this->redirect(baseUrl('tickets'));
        }
        
        // Check permissions
        if ($user['role'] === 'client' && $attachment['client_id'] != $user['id']) {
            $this->setFlash('error', 'You do not have permission to download this attachment');
            $this->redirect(baseUrl('tickets'));
        }
        
        $filePath = __DIR__ . '/../../uploads/tickets/' . $attachment['filename'];
        
        if (!file_exists($filePath)) {
            $this->setFlash('error', 'File not found');
            $this->redirect(baseUrl('tickets/' . $attachment['ticket_id']));
        }
        
        // Set headers for download
        header('Content-Type: ' . $attachment['mime_type']);
        header('Content-Disposition: attachment; filename="' . $attachment['original_filename'] . '"');
        header('Content-Length: ' . filesize($filePath));
        
        // Output file
        readfile($filePath);
        exit;
    }
    
    /**
     * Handle ticket reply
     */
    public function reply($id) {
    $this->requireAuth();
    
    try {
        $this->validateCSRF();
        
        $user = getCurrentUser();
        $message = sanitize($this->getPost('message'));
        $isInternal = ($this->getPost('is_internal') === '1') ? 1 : 0;
        
        // Get ticket
        $ticket = DB::table('tickets')->where('id', $id)->first();
        if (!$ticket) {
            throw new Exception('Ticket not found');
        }
        
        // Check permissions
        if ($user['role'] === 'client' && $ticket['client_id'] != $user['id']) {
            throw new Exception('You do not have permission to reply to this ticket');
        }
        
        // Clients cannot post internal notes
        if ($user['role'] === 'client') {
            $isInternal = 0;
        }
        
        if (empty($message)) {
            throw new Exception('Message is required');
        }
        
        // Add reply
        $replyId = DB::table('ticket_replies')->insert([
            'ticket_id' => $id,
            'user_id' => $user['id'],
            'message' => $message,
            'is_internal' => $isInternal
        ]);
        
        // Handle file uploads for reply
        if (!empty($_FILES['attachments']['name'][0])) {
            $this->handleFileUploads($id, $user['id'], $replyId);
        }
        
        // Update ticket status if it's closed and client is replying
        if ($ticket['status'] === 'closed' && $user['role'] === 'client') {
            DB::update('tickets', [
                'status' => 'open'
            ], ['id' => $id]);
        }
        
        // Send notifications
        $notificationManager = NotificationManager::getInstance();
        $notificationManager->notifyTicketReply($id, $replyId, $user['id']);
        
        $this->setFlash('success', 'Reply added successfully');
        $this->redirect(baseUrl('tickets/' . $id));
        
    } catch (Exception $e) {
        $this->setFlash('error', $e->getMessage());
        $this->redirect(baseUrl('tickets/' . $id));
    }
}
    
    /**
     * Handle ticket status update (Admin/Support only)
     */
    public function updateStatus($id) {
    $this->requireAuth();
    
    try {
        $this->validateCSRF();
        
        $user = getCurrentUser();
        
        // Only admin and support can update status
        if (!in_array($user['role'], ['admin', 'support'])) {
            throw new Exception('You do not have permission to update ticket status');
        }
        
        // Get current ticket status
        $currentTicket = DB::table('tickets')->where('id', $id)->first();
        if (!$currentTicket) {
            throw new Exception('Ticket not found');
        }
        
        $oldStatus = $currentTicket['status'];
        $status = sanitize($this->getPost('status'));
        $assignedTo = $this->getPost('assigned_to') ? (int) $this->getPost('assigned_to') : null;
        
        if (!in_array($status, ['open', 'in_progress', 'resolved', 'closed', 'cancelled'])) {
            throw new Exception('Invalid status');
        }
        
        $updateData = [
            'status' => $status
        ];
        
        if ($assignedTo !== null && $assignedTo != $currentTicket['assigned_to']) {
            $updateData['assigned_to'] = $assignedTo;
            
            // Send assignment notification
            if ($assignedTo > 0) {
                $notificationManager = NotificationManager::getInstance();
                $notificationManager->notifyTicketAssignment($id, $assignedTo, $user['id']);
            }
        }
        
        if ($status === 'resolved') {
            $updateData['resolved_at'] = date('Y-m-d H:i:s');
        }
        
        if ($status === 'closed') {
            $updateData['closed_at'] = date('Y-m-d H:i:s');
        }
        
        DB::update('tickets', $updateData, ['id' => $id]);
        
        // Send status change notification
        if ($oldStatus !== $status) {
            $notificationManager = NotificationManager::getInstance();
            $notificationManager->notifyTicketStatusChange($id, $oldStatus, $status, $user['id']);
        }
        
        $this->setFlash('success', 'Ticket status updated successfully');
        $this->redirect(baseUrl('tickets/' . $id));
        
    } catch (Exception $e) {
        $this->setFlash('error', $e->getMessage());
        $this->redirect(baseUrl('tickets/' . $id));
    }
}
    
    /**
     * Generate unique ticket number
     */
    private function generateTicketNumber() {
        $prefix = 'TK';
        $year = date('Y');
        $month = date('m');
        
        // Get the last ticket number for this month
        $lastTicket = DB::table('tickets')
            ->where('ticket_number', 'LIKE', $prefix . $year . $month . '%')
            ->orderBy('ticket_number', 'DESC')
            ->limit(1)
            ->first();
        
        if ($lastTicket) {
            $lastNumber = (int) substr($lastTicket['ticket_number'], -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . $year . $month . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
}