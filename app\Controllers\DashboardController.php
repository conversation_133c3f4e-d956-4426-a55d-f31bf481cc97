<?php
/**
 * Dashboard Controller
 */

require_once __DIR__ . '/../Core/Controller.php';

class DashboardController extends Controller {
    
    /**
     * Show dashboard based on user role
     */
    public function index() {
        $this->requireAuth();
        
        $user = getCurrentUser();
        
        switch ($user['role']) {
            case 'admin':
                $this->adminDashboard();
                break;
            case 'support':
                $this->supportDashboard();
                break;
            case 'client':
                $this->clientDashboard();
                break;
            default:
                $this->redirect(baseUrl('unauthorized'));
        }
    }
    
    /**
     * Admin dashboard
     */
    private function adminDashboard() {
        // Get statistics
        $stats = [
            'total_users' => DB::table('users')->count(),
            'total_tickets' => DB::table('tickets')->count(),
            'open_tickets' => DB::table('tickets')->where('status', 'open')->count(),
            'closed_tickets' => DB::table('tickets')->where('status', 'closed')->count(),
        ];
        
        // Get recent users
        $recentUsers = DB::table('users')
            ->select(['name', 'email', 'role', 'created_at'])
            ->orderBy('created_at', 'DESC')
            ->limit(5)
            ->get();
        
        $this->render('dashboard/admin', [
            'title' => 'Admin Dashboard',
            'stats' => $stats,
            'recentUsers' => $recentUsers
        ]);
    }
    
    /**
     * Support dashboard
     */
    private function supportDashboard() {
        $user = getCurrentUser();
        
        // Get statistics
        $stats = [
            'assigned_tickets' => DB::table('tickets')->where('assigned_to', $user['id'])->count(),
            'resolved_tickets' => DB::table('tickets')->where('assigned_to', $user['id'])->where('status', 'resolved')->count(),
            'pending_tickets' => DB::table('tickets')->where('status', 'open')->count(),
        ];
        
        $this->render('dashboard/support', [
            'title' => 'Support Dashboard',
            'stats' => $stats
        ]);
    }
    
    /**
     * Client dashboard
     */
    private function clientDashboard() {
        $user = getCurrentUser();
        
        // Get statistics
        $stats = [
            'my_tickets' => DB::table('tickets')->where('client_id', $user['id'])->count(),
            'open_tickets' => DB::table('tickets')->where('client_id', $user['id'])->where('status', 'open')->count(),
            'closed_tickets' => DB::table('tickets')->where('client_id', $user['id'])->where('status', 'closed')->count(),
        ];
        
        $this->render('dashboard/client', [
            'title' => 'My Dashboard',
            'stats' => $stats
        ]);
    }
    
    /**
     * Unauthorized access page
     */
    public function unauthorized() {
        $this->render('errors/unauthorized', [
            'title' => 'Unauthorized Access'
        ]);
    }
}
