<?php
/**
 * Configuration file for the PHP project
 */

// Site configuration
$config = [
    'site_title' => 'Ticketing App',
    'site_description' => 'Call Center Ticketing System',
    'base_url' => 'http://ticketing.local',  // Set for local development
    'debug_mode' => true,
];

// Database configuration
$db_config = [
    'host' => 'localhost',  // Use MySQL service name for Docker
    'username' => 'root',  // Match Docker compose MySQL user
    'password' => '',  // Match Docker compose MySQL password
    'database' => 'ticketing_db',  // Match Docker compose MySQL database
    'charset' => 'utf8mb4',
    'port' => 3306,
];



// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting based on debug mode
if ($config['debug_mode']) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', 'error.log');
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

