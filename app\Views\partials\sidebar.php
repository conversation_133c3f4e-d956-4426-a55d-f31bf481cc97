<?php
/**
 * Sidebar partial
 */
$user = getCurrentUser();
$currentUrl = currentUrl();
?>
<nav class="sidebar sidebar-offcanvas" id="sidebar">
    <ul class="nav ">
        <li class="nav-item nav-category">Main</li>
        <li class="nav-item <?= strpos($currentUrl, 'dashboard') !== false ? 'active' : ''; ?>">
            <a class="nav-link" href="<?= baseUrl('dashboard'); ?>">
                <span class="icon-bg"><i class="mdi mdi-cube menu-icon"></i></span>
                <span class="menu-title">Dashboard</span>
            </a>
        </li>

        <li class="nav-item nav-category">Ticketing</li>

        <?php if ($user['role'] === 'admin'): ?>
        <li class="nav-item <?= (strpos($currentUrl, 'users') !== false || strpos($currentUrl, 'categories') !== false || strpos($currentUrl, 'priorities') !== false) ? 'active' : ''; ?>">
            <a class="nav-link" data-toggle="collapse" href="#admin" aria-expanded="<?= (strpos($currentUrl, 'users') !== false || strpos($currentUrl, 'categories') !== false || strpos($currentUrl, 'priorities') !== false) ? 'true' : 'false'; ?>" aria-controls="admin">
                <span class="icon-bg"><i class="mdi mdi-shield-account menu-icon"></i></span>
                <span class="menu-title">Administration</span>
                <i class="menu-arrow"></i>
            </a>
            <div class="collapse <?= (strpos($currentUrl, 'users') !== false || strpos($currentUrl, 'categories') !== false || strpos($currentUrl, 'priorities') !== false) ? 'show' : ''; ?>" id="admin">
                <ul class="nav flex-column sub-menu">
                    <li class="nav-item"> <a class="nav-link <?= strpos($currentUrl, 'users') !== false && strpos($currentUrl, 'create') === false ? 'active' : ''; ?>" href="<?= baseUrl('users'); ?>">Manage Users</a></li>
                    <li class="nav-item"> <a class="nav-link <?= strpos($currentUrl, 'users/create') !== false ? 'active' : ''; ?>" href="<?= baseUrl('users/create'); ?>">Create User</a></li>
                    <li class="nav-item"> <a class="nav-link <?= strpos($currentUrl, 'categories') !== false && strpos($currentUrl, 'create') === false ? 'active' : ''; ?>" href="<?= baseUrl('categories'); ?>">Manage Categories</a></li>
                    <li class="nav-item"> <a class="nav-link <?= strpos($currentUrl, 'priorities') !== false && strpos($currentUrl, 'create') === false ? 'active' : ''; ?>" href="<?= baseUrl('priorities'); ?>">Manage Priorities</a></li>
                </ul>
            </div>
        </li>
        <?php endif; ?>

        <!-- Tickets Section for All Users -->
        <li class="nav-item">
            <a class="nav-link" href="<?= baseUrl('tickets'); ?>">
                <span class="icon-bg"><i class="mdi mdi-ticket menu-icon"></i></span>
                <span class="menu-title">
                    <?php if ($user['role'] === 'client'): ?>
                        My Tickets
                    <?php else: ?>
                        Tickets
                    <?php endif; ?>
                </span>
            </a>
        </li>

        <?php if ($user['role'] === 'client'): ?>
        <li class="nav-item">
            <a class="nav-link" href="<?= baseUrl('tickets/create'); ?>">
                <span class="icon-bg"><i class="mdi mdi-plus menu-icon"></i></span>
                <span class="menu-title">Create Ticket</span>
            </a>
        </li>
        <?php endif; ?>

        <li class="nav-item sidebar-user-actions">
            <div class="user-details">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="d-flex align-items-center">
                            <div class="sidebar-profile-img">
                                <img src="<?= baseUrl('assets/images/agent.png'); ?>" alt="image">
                            </div>
                            <div class="sidebar-profile-text">
                                <p class="mb-1"><?= sanitize($user['name']); ?></p>
                                <p class="mb-0 text-muted"><?= ucfirst($user['role']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </li>
        <li class="nav-item sidebar-user-actions">
            <div class="sidebar-user-menu">
                <a href="<?= baseUrl('profile'); ?>" class="nav-link <?= strpos($currentUrl, 'profile') !== false ? 'active' : ''; ?>"><i class="mdi mdi-settings menu-icon"></i>
                    <span class="menu-title">Profile</span>
                </a>
            </div>
        </li>
        <li class="nav-item sidebar-user-actions">
            <div class="sidebar-user-menu">
                <a href="<?= baseUrl('logout'); ?>" class="nav-link"><i class="mdi mdi-logout menu-icon"></i>
                    <span class="menu-title">Log Out</span>
                </a>
            </div>
        </li>
    </ul>
</nav>
