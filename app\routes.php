<?php
/**
 * Application Routes
 */

// Authentication Routes
Router::get('/', 'Dashboard@index');
Router::get('/login', 'User@login');
Router::post('/login', 'User@doLogin');
Router::get('/logout', 'User@logout');

// Dashboard Routes
Router::get('/dashboard', 'Dashboard@index');
Router::get('/unauthorized', 'Dashboard@unauthorized');

// User Management Routes (Admin only)
Router::get('/users', 'User@index');
Router::get('/users/create', 'User@create');
Router::post('/users', 'User@store');
Router::get('/users/edit/{id}', 'User@edit');
Router::post('/users/update/{id}', 'User@update');
Router::post('/users/delete/{id}', 'User@delete');

// Profile Routes
Router::get('/profile', 'User@profile');
Router::post('/profile', 'User@updateProfile');

// Ticket Routes
Router::get('/tickets', 'Ticket@index');
Router::get('/tickets/create', 'Ticket@create');
Router::post('/tickets', 'Ticket@store');
Router::get('/tickets/{id}', 'Ticket@show');
Router::post('/tickets/{id}/reply', 'Ticket@reply');
Router::post('/tickets/{id}/status', 'Ticket@updateStatus');
Router::get('/tickets/attachment/{id}/download', 'Ticket@downloadAttachment');

// Notification Routes
Router::get('/notifications', 'Notification@index');
Router::get('/notifications/unread-count', 'Notification@getUnreadCount');
Router::get('/notifications/websocket-token', 'Notification@getWebSocketToken');
Router::post('/notifications/{id}/read', 'Notification@markAsRead');
Router::post('/notifications/mark-all-read', 'Notification@markAllAsRead');
