<div class="row">
    <div class="col-md-12 grid-margin">
        <div class="d-flex justify-content-between flex-wrap">
            <div class="d-flex align-items-end flex-wrap">
                <div class="mr-md-3 mr-xl-5">
                    <h2>Welcome back, <?= sanitize($user['name']); ?>!</h2>
                    <p class="mb-md-0">Your client dashboard overview.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">My Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['my_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-info">
                                <span>Total</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Open Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['open_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-warning">
                                <span>Pending</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Closed Tickets</h6>
                </div>
                <div class="row">
                    <div class="col-6 col-md-12 col-xl-5">
                        <h3 class="mb-2"><?= $stats['closed_tickets']; ?></h3>
                        <div class="d-flex align-items-baseline">
                            <p class="text-success">
                                <span>Resolved</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-xl-8 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Recent Tickets</h6>
                </div>
                <div class="d-flex flex-column">
                    <div class="alert alert-info">
                        <i data-feather="info" class="icon-sm mr-2"></i>
                        No recent tickets to display. Create your first ticket to get started.
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 col-xl-4 grid-margin stretch-card">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-baseline">
                    <h6 class="card-title mb-0">Quick Actions</h6>
                </div>
                <div class="d-flex flex-column">
                    <a href="<?= baseUrl('tickets/create'); ?>" class="btn btn-primary mb-2">
                        <i data-feather="plus" class="icon-sm mr-2"></i>
                        Create New Ticket
                    </a>
                    <a href="<?= baseUrl('tickets'); ?>" class="btn btn-outline-primary mb-2">
                        <i data-feather="file-text" class="icon-sm mr-2"></i>
                        View My Tickets
                    </a>
                    <a href="<?= baseUrl('profile'); ?>" class="btn btn-outline-secondary">
                        <i data-feather="user" class="icon-sm mr-2"></i>
                        Update Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
